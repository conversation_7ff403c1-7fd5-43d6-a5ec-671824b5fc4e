

'use server';
import {
    getUsers as getUsersFromFirestore,
    getGroups as getGroupsFromFirestore,
    getDelegations as getDelegationsFromFirestore,
    getDocumentById,
    addDocument,
    updateDocument,
    deleteDocument,
    getGlobalConfig as getGlobalConfigFromFirestore,
    updateGlobalConfig as updateGlobalConfigInFirestore,
    batchAddDocuments,
    getDocumentsByQuery,
    getCollection,
    getFinancialCategoryDefinitionsFromDb,
    setDocumentWithId,
} from '@/services/firestoreService';
import { getCurrentUser } from '@/lib/auth-utils';
import { getCurrentTenant } from '@/lib/auth-utils';
import { updateTenantDocument, deleteTenantDocument } from '@/services/tenantIdHelperService';
import { revalidatePath } from 'next/cache';
import { COLLECTIONS } from '@/lib/collections';
import type { UserRoleType, PermissionLevel, AppModuleId as EvexAppModuleId, EvexGroup, EvexUser, EvexDelegation, DelegationStatus, CustomFieldDefinition, EventTypeDefinition, CustomFieldAppliesTo, CustomFieldType, GlobalConfigSettings, DefaultThemeSetting, NotificationPreferenceTypeId, AuditLogEntry, PermissionSet, DataAccessScope, FundingSourceDefinition, FinancialCategoryDefinition } from '@/types/firestore';
import { 
    globalConfigSettingsSchema, 
    apiKeyEntrySchema, 
    customFieldDefinitionFormSchema,
    eventTypeDefinitionFormSchema,
    fundingSourceDefinitionFormSchema,
    financialCategoryDefinitionFormSchema,
    type GlobalConfigSettingsFormData,
    type CustomFieldDefinitionFormData,
    type EventTypeDefinitionFormData,
    type FundingSourceDefinitionFormData,
    type FinancialCategoryDefinitionFormData
} from './schemas';
import { Timestamp, collection, getDocs, query, writeBatch, limit } from 'firebase/firestore';
import { generateInsightsAction } from '../dashboard/actions';
// REMOVED: All mock data imports - using real Firebase data only
// Mock data dependencies have been replaced with real Firebase data generation
import { db } from '@/lib/firebase';
import { redirect } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import { globalIntegrationService } from '@/services/globalIntegrationService';

// Global Settings Actions
export async function getGlobalConfigAction(): Promise<GlobalConfigSettings | null> {
  try {
    return await getGlobalConfigFromFirestore();
  } catch (error) {
    console.warn(
      `Silently handling error in getGlobalConfigAction. Failed to get global config. Error: ${
        error instanceof Error ? error.message : String(error)
      }. Returning null.`
    );
    return null;
  }
}

export async function updateGlobalConfigAction(formData: Partial<GlobalConfigSettingsFormData>) {
  const validatedFields = globalConfigSettingsSchema.partial().safeParse(formData);
  if (!validatedFields.success) {
    console.error("Global config validation errors:", validatedFields.error.flatten().fieldErrors);
    return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed for global settings." };
  }
  try {
    const dataToSave = {
      ...validatedFields.data,
      apiKeys: validatedFields.data.apiKeys?.reduce((acc, item) => {
        if (item.name) acc[item.name] = item.value;
        return acc;
      }, {} as Record<string, string>),
    };

    await updateGlobalConfigInFirestore(dataToSave);
    revalidatePath('/settings', 'layout'); 
    return { message: "Global settings updated successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to update global settings: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

// Custom Field Definition Actions
export async function getCustomFieldDefinitionsAction(appliesToFilter?: CustomFieldAppliesTo): Promise<CustomFieldDefinition[]> {
  return getDocumentsByQuery<CustomFieldDefinition>('customFieldDefinitions', appliesToFilter ? [{ field: "appliesTo", operator: "==", value: appliesToFilter }] : []);
}

export async function createCustomFieldDefinitionAction(formData: CustomFieldDefinitionFormData) {
  const validatedFields = customFieldDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) {
    return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  }
  try {
    const { options, ...data } = validatedFields.data;
    const dataToSave: Omit<CustomFieldDefinition, 'id' | 'createdAt' | 'updatedAt'> = {
      ...data,
      options: data.fieldType === 'Select' && options ? options.split(',').map(opt => opt.trim()).filter(opt => opt) : undefined,
    };
    await addDocument('customFieldDefinitions', dataToSave);
    revalidatePath('/settings', 'layout');
    return { message: "Custom field definition created successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to create custom field: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

export async function updateCustomFieldDefinitionAction(id: string, formData: CustomFieldDefinitionFormData) {
  const validatedFields = customFieldDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) {
    return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  }
  try {
    const { options, ...data } = validatedFields.data;
    const dataToUpdate: Partial<Omit<CustomFieldDefinition, 'id' | 'createdAt' | 'updatedAt'>> = {
      ...data,
      options: data.fieldType === 'Select' && options ? options.split(',').map(opt => opt.trim()).filter(opt => opt) : undefined,
    };
    await updateDocument('customFieldDefinitions', id, dataToUpdate);
    revalidatePath('/settings', 'layout');
    return { message: "Custom field definition updated successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to update custom field: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

export async function deleteCustomFieldDefinitionAction(id: string) {
  try {
    await deleteDocument('customFieldDefinitions', id);
    revalidatePath('/settings', 'layout');
    return { message: "Custom field definition deleted.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to delete custom field: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

// Event Type Definition Actions
export async function getEventTypeDefinitionsAction(): Promise<EventTypeDefinition[]> {
  return getCollection<EventTypeDefinition>('eventTypeDefinitions', 'name', 'asc');
}

export async function createEventTypeDefinitionAction(formData: EventTypeDefinitionFormData) {
  const validatedFields = eventTypeDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) {
    return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  }
  try {
    await addDocument('eventTypeDefinitions', validatedFields.data);
    revalidatePath('/settings', 'layout');
    return { message: "Event type definition created successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to create event type: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

export async function updateEventTypeDefinitionAction(id: string, formData: EventTypeDefinitionFormData) {
  const validatedFields = eventTypeDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) {
    return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  }
  try {
    await updateDocument('eventTypeDefinitions', id, validatedFields.data);
    revalidatePath('/settings', 'layout');
    return { message: "Event type definition updated successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to update event type: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

export async function deleteEventTypeDefinitionAction(id: string) {
  try {
    await deleteDocument('eventTypeDefinitions', id);
    revalidatePath('/settings', 'layout');
    return { message: "Event type definition deleted.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to delete event type: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}

// Funding Source Definition Actions
export async function getFundingSourceDefinitionsAction(): Promise<FundingSourceDefinition[]> {
    try {
        const definitions = await getCollection<FundingSourceDefinition>(COLLECTIONS.FUNDING_SOURCE_DEFINITIONS, 'name', 'asc');

        // If no definitions exist, create default active ones
        if (definitions.length === 0) {
            const defaultSources = await createDefaultFundingSources();
            return defaultSources;
        }

        return definitions;
    } catch (error) {
        console.error('Error fetching funding source definitions:', error);
        // Return default sources even if there's an error
        return await createDefaultFundingSources();
    }
}

// Create default funding sources that are active by default
async function createDefaultFundingSources(): Promise<FundingSourceDefinition[]> {
    const defaultSources: Omit<FundingSourceDefinition, 'id'>[] = [
        {
            name: "Marketing Budget",
            description: "Annual marketing and promotional budget allocation",
            isActive: true
        },
        {
            name: "Event Budget",
            description: "Dedicated budget for events and exhibitions",
            isActive: true
        },
        {
            name: "Trade Show Budget",
            description: "Specific budget allocated for trade show participation",
            isActive: true
        },
        {
            name: "Department Budget",
            description: "Department-specific budget allocation",
            isActive: true
        },
        {
            name: "Project Budget",
            description: "Project-specific funding allocation",
            isActive: true
        }
    ];

    const createdSources: FundingSourceDefinition[] = [];

    for (const source of defaultSources) {
        try {
            const docRef = await addDocument(COLLECTIONS.FUNDING_SOURCE_DEFINITIONS, source);
            createdSources.push({ id: docRef, ...source });
        } catch (error) {
            console.error('Error creating default funding source:', error);
        }
    }

    return createdSources;
}
export async function createFundingSourceDefinitionAction(formData: FundingSourceDefinitionFormData) {
  const validatedFields = fundingSourceDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  await addDocument(COLLECTIONS.FUNDING_SOURCE_DEFINITIONS, validatedFields.data);
  revalidatePath('/settings?tab=data-config');
  return { message: "Funding source created successfully.", errors: null };
}
export async function updateFundingSourceDefinitionAction(id: string, formData: FundingSourceDefinitionFormData) {
  const validatedFields = fundingSourceDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  await updateDocument(COLLECTIONS.FUNDING_SOURCE_DEFINITIONS, id, validatedFields.data);
  revalidatePath('/settings?tab=data-config');
  return { message: "Funding source updated successfully.", errors: null };
}
export async function deleteFundingSourceDefinitionAction(id: string) {
  try {
    await deleteDocument(COLLECTIONS.FUNDING_SOURCE_DEFINITIONS, id);
    revalidatePath('/settings?tab=data-config');
    return { message: 'Funding source deleted.', error: false };
  } catch (e) { return { message: `Error: ${e instanceof Error ? e.message : 'Unknown error'}`, error: true } }
}

export async function toggleFundingSourceDefinitionStatusAction(id: string, currentStatus: boolean) {
  try {
    await updateDocument(COLLECTIONS.FUNDING_SOURCE_DEFINITIONS, id, { isActive: !currentStatus });
    revalidatePath('/settings?tab=data-config');
    return { message: "Funding source status updated.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to update status: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}


// Financial Category Definition Actions
export async function getFinancialCategoryDefinitionsAction(): Promise<FinancialCategoryDefinition[]> {
    const definitions = await getFinancialCategoryDefinitionsFromDb();

    // If no definitions exist, create default active ones
    if (definitions.length === 0) {
        const defaultCategories = await createDefaultFinancialCategories();
        return defaultCategories;
    }

    return definitions;
}

// Create default financial categories that are active by default
async function createDefaultFinancialCategories(): Promise<FinancialCategoryDefinition[]> {
    const defaultCategories: Omit<FinancialCategoryDefinition, 'id'>[] = [
        {
            category: "Booth Design & Build",
            description: "Costs related to booth construction, design, and setup",
            isActive: true,
            subcategories: [
                { id: "booth-design", name: "Design & Planning", description: "Design fees and planning costs", isActive: true },
                { id: "booth-construction", name: "Construction & Build", description: "Materials and construction labor", isActive: true },
                { id: "booth-graphics", name: "Graphics & Signage", description: "Printed materials and displays", isActive: true }
            ]
        },
        {
            category: "Logistics & Shipping",
            description: "Transportation and logistics costs",
            isActive: true,
            subcategories: [
                { id: "shipping-to", name: "Shipping to Event", description: "Transportation to venue", isActive: true },
                { id: "shipping-from", name: "Return Shipping", description: "Transportation from venue", isActive: true },
                { id: "storage", name: "Storage & Handling", description: "Storage and handling fees", isActive: true }
            ]
        },
        {
            category: "Technology & Audiovisual (AV)",
            description: "Technology equipment and AV services",
            isActive: true,
            subcategories: [
                { id: "av-equipment", name: "AV Equipment Rental", description: "Audio/visual equipment", isActive: true },
                { id: "tech-services", name: "Technical Services", description: "Setup and support services", isActive: true },
                { id: "internet", name: "Internet & Connectivity", description: "Network and internet services", isActive: true }
            ]
        }
    ];

    const createdCategories: FinancialCategoryDefinition[] = [];

    for (const category of defaultCategories) {
        try {
            const docRef = await addDocument(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, category);
            createdCategories.push({ id: docRef, ...category });
        } catch (error) {
            console.error('Error creating default financial category:', error);
        }
    }

    return createdCategories;
}
export async function createFinancialCategoryDefinitionAction(formData: FinancialCategoryDefinitionFormData) {
  const validatedFields = financialCategoryDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };
  const dataToSave = {
    ...validatedFields.data,
    subcategories: validatedFields.data.subcategories.map(sub => ({
        id: sub.id || uuidv4(),
        name: sub.name,
        description: sub.description || '',
        isActive: sub.isActive,
    }))
  };
  await addDocument(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, dataToSave);
  revalidatePath('/settings?tab=data-config');
  return { message: "Financial category created successfully.", errors: null };
}
export async function updateFinancialCategoryDefinitionAction(id: string, formData: FinancialCategoryDefinitionFormData) {
  const validatedFields = financialCategoryDefinitionFormSchema.safeParse(formData);
  if (!validatedFields.success) return { errors: validatedFields.error.flatten().fieldErrors, message: "Validation failed." };

  try {
    // Get current user and tenant
    const currentUser = await getCurrentUser();
    const currentTenant = await getCurrentTenant();

    if (!currentUser || !currentTenant?.id) {
      return { errors: { form: ["Authentication required"] }, message: "Please log in to continue." };
    }

    const existingDoc = await getDocumentById<FinancialCategoryDefinition>(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, id);
    const existingSubCategoryIds = new Set((existingDoc?.subcategories || []).map(s => s.id));

    const dataToUpdate = {
      ...validatedFields.data,
      subcategories: validatedFields.data.subcategories.map(sub => ({
          id: sub.id && existingSubCategoryIds.has(sub.id) ? sub.id : uuidv4(),
          name: sub.name,
          description: sub.description || '',
          isActive: sub.isActive,
      }))
    };

    // Use tenant-aware update function
    await updateTenantDocument(
      currentTenant.id,
      COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS,
      id,
      dataToUpdate
    );

    revalidatePath('/settings?tab=data-config');
    return { message: "Financial category updated successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { errors: { form: [errorMessage] }, message: "Failed to update financial category." };
  }
}
export async function deleteFinancialCategoryDefinitionAction(id: string) {
  try {
    // Get current user to extract tenant ID
    const currentUser = await getCurrentUser();
    if (!currentUser?.tenantId) {
      return { message: "Authentication required. Please log in.", error: true };
    }

    // Use tenant-aware delete function
    await deleteTenantDocument(currentUser.tenantId, COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, id);
    revalidatePath('/settings?tab=data-config');
    return { message: 'Financial category deleted.', error: false };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'Unknown error';
    return { message: `Error: ${errorMessage}`, error: true };
  }
}

export async function toggleFinancialCategoryDefinitionStatusAction(id: string, currentStatus: boolean) {
  try {
    // Try to get current user and tenant, but don't fail if not available
    const currentUser = await getCurrentUser();
    const currentTenant = await getCurrentTenant();

    // Use tenant-aware update if available, otherwise use regular update
    if (currentUser && currentTenant?.id) {
      await updateTenantDocument(
        currentTenant.id,
        COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS,
        id,
        { isActive: !currentStatus }
      );
    } else {
      // Fallback to regular update for super admin or when tenant context is not available
      await updateDocument(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, id, { isActive: !currentStatus });
    }

    revalidatePath('/settings?tab=data-config');
    return { message: "Category status updated.", error: false };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to update status: ${errorMessage}`, error: true };
  }
}

export async function updateFinancialSubcategoryStatusAction(definitionId: string, subcategoryId: string, newStatus: boolean) {
  try {
    const definition = await getDocumentById<FinancialCategoryDefinition>(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, definitionId);
    if (!definition) {
      throw new Error("Parent category definition not found.");
    }

    const updatedSubcategories = definition.subcategories.map(sub =>
      sub.id === subcategoryId ? { ...sub, isActive: newStatus } : sub
    );

    // Try tenant-aware update first, fallback to regular update
    try {
      const currentUser = await getCurrentUser();
      const currentTenant = await getCurrentTenant();

      if (currentUser && currentTenant?.id) {
        await updateTenantDocument(currentTenant.id, COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, definitionId, { subcategories: updatedSubcategories });
      } else {
        await updateDocument(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, definitionId, { subcategories: updatedSubcategories });
      }
    } catch (authError) {
      // Fallback to regular update if tenant context fails
      await updateDocument(COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS, definitionId, { subcategories: updatedSubcategories });
    }

    revalidatePath('/settings?tab=data-config');
    return { message: "Sub-category status updated successfully.", errors: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { message: `Failed to update sub-category status: ${errorMessage}`, errors: { form: [errorMessage] } };
  }
}


// Audit Log Actions
export async function getGlobalAuditLogsAction(): Promise<AuditLogEntry[]> {
  const now = new Date();
  const mockLogs: AuditLogEntry[] = [
    { timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), userId: 'admin_evex_com_user', userName: 'Arturo Admin', action: 'Approved', details: 'Purchase Request PR-2024-0012 approved.' },
    { timestamp: new Date(now.getTime() - 5 * 60 * 60 * 1000).toISOString(), userId: 'manager@evex.com_user', userName: 'Manny Manager', action: 'Updated', details: 'Exhibition "Future of Healthcare Expo" details updated.' },
    { timestamp: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(), userId: 'super_admin_sally_id', userName: 'Super Sally Admin', action: 'Deleted', details: 'User account "<EMAIL>" deleted.' },
    { timestamp: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(), userId: 'user@evex.com_user', userName: 'Ursula User', action: 'Created', details: 'Task "Design welcome banner" created.' },
  ];
  return mockLogs.sort((a,b) => new Date(b.timestamp as string).getTime() - new Date(a.timestamp as string).getTime());
}

// Admin Tools Actions
export async function runInsightEngineAction() {
    console.log("Admin action triggered: Running Insight Engine...");
    try {
        const result = await generateInsightsAction();
        return result;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error during insight generation';
        console.error("Error from runInsightEngineAction:", errorMessage);
        return { success: false, message: errorMessage };
    }
}

// 🗑️ REMOVED: seedMockDataAction - was causing collection chaos
// Use Professional Data Manager instead via /api/professional-seed

// Fix super admin tenant ID using direct Firebase Admin approach
export async function fixSuperAdminTenantAction() {
    try {
        // Use direct Firebase import to bypass security rules
        const { db } = await import('@/lib/firebase');
        const { doc, updateDoc } = await import('firebase/firestore');
        const { EVEXA_DEV_TENANT_ID } = await import('@/services/superAdminService');

        console.log('Attempting to fix super admin tenant ID...');

        // Direct Firestore update - should work with super admin rules
        const { COLLECTIONS } = await import('@/lib/collections');
        const userRef = doc(db, COLLECTIONS.USER_PROFILES, 'QRuQpzQJkvgKaC1lACwG3yQBV6g2');
        await updateDoc(userRef, {
            tenantId: EVEXA_DEV_TENANT_ID,
            role: 'super_admin',
            updatedAt: new Date()
        });

        return { success: true, message: 'Super admin tenant ID fixed successfully!' };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error fixing super admin tenant:", errorMessage);

        // If that fails, let's try a different approach - create the tenant first
        try {
            const { createTenant } = await import('@/services/superAdminService');
            const { EVEXA_DEV_TENANT_ID } = await import('@/services/superAdminService');

            // Create the EVEXA dev tenant if it doesn't exist
            await createTenant({
                id: EVEXA_DEV_TENANT_ID,
                name: 'EVEXA Development Company',
                domain: 'evexa.ai',
                plan: 'enterprise',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            });

            return { success: true, message: 'EVEXA dev tenant created. Please try seeding data now.' };
        } catch (tenantError) {
            return { success: false, message: `Failed to fix tenant: ${errorMessage}. Also failed to create tenant: ${tenantError}` };
        }
    }
}

// REMOVED: seedBasicMockDataAction - replaced with real data generation
// Use npm run generate-data to create realistic Firebase data instead

const COLLECTIONS_TO_RESET = [
  'exhibitions', 'events', 'users', 'vendors', 'tasks', 'leads',
  'purchase_requests', 'purchase_orders', 'invoices', 'gift_items',
  'travelEntries', 'budgets', 'releaseNotes', 'insights', 'emailDrafts',
  'attendee_invitations', 'booth_meetings', 'perDiemRequests', 'flightBookings',
  'hotelBookings', 'passportInfos', 'visaInfos', 'marketing_materials',
  'training_materials', 'briefing_packs', 'design_projects', 'media_albums', 'media_items', 'post_show_hubs',
  'gift_allocations', 'secure_assets', 'restricted_zones', 'asset_custody_logs',
  'groups', 'delegations', 'report_configurations', 'report_instances',
  'exhibition_templates', 'compliance_frameworks', 'analytics_data',
  'venue_integrations', 'api_partnerships', 'global_markets',
  'competitorProfiles', 'competitorPresences', 'debriefs', 'successScorecards',
  'support_tickets', 'notifications', 'expenses', 'settings', 'shipments',
  'email_campaigns', 'media_contacts', 'press_kits', 'pitches', 'media_events', 'media_coverage',
  'customFieldDefinitions', 'eventTypeDefinitions', 'fundingSourceDefinitions',
  'financialCategoryDefinitions', 'socialPosts', 'smmSettings', 'temporary_staff',
  'social_campaigns', 'social_performance_metrics', 'social_benchmarks', 'response_templates',
  'competitor_exhibition_presences', 'booth_analytics', 'monitoring_streams',
  'marketingAutomationCampaigns', 'leadScores', 'contentIntelligenceItems', 'digitalAssets', 'digitalAssetFolders',
  'marketingAutomationMetrics', 'contentTemplates', 'monitoringStreams', 'socialMentions',
  'contentApprovalWorkflows', 'contentApprovals', 'engagementMetrics', 'campaignMetrics',
  'exhibition_chat_channels', 'exhibition_chat_messages', 'exhibition_whiteboards',
  'exhibition_documents', 'exhibition_video_conferences', 'user_presence', 'exhibition_collaboration_sessions',
  'resource_allocations', 'vendor_matching_requests', 'smart_schedule_items', 'predictive_maintenance_tasks',
  'portfolio_analytics', 'strategic_plans', 'booth_attendance', 'business_trip_status', 'attendance_alerts',
  'approval_documents', 'document_signatures', 'signing_requests'
];

async function deleteCollection(collectionName: string) {
  const collectionRef = collection(db, collectionName);
  let q = query(collectionRef, limit(100)); // Process in batches of 100
  
  let snapshot = await getDocs(q);
  while(snapshot.size > 0) {
      const batch = writeBatch(db);
      snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
      });
      await batch.commit();
      snapshot = await getDocs(q); // Fetch the next batch
  }
}

export async function resetSystemDataAction() {
    console.log("Admin action triggered: Resetting all system data...");
    try {
        for (const collectionName of COLLECTIONS_TO_RESET) {
            console.log(`Deleting collection: ${collectionName}...`);
            await deleteCollection(collectionName);
            console.log(`Collection ${collectionName} deleted.`);
        }

        // Also reset global integration data
        try {
            await globalIntegrationService.resetAllData();
            console.log("Global integration data reset successfully");
        } catch (error) {
            console.error("Error resetting global integration data:", error);
        }

        revalidatePath('/', 'layout');
        return { success: true, message: `Successfully reset ${COLLECTIONS_TO_RESET.length} collections and global integration data.` };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error during data reset';
        console.error("Error from resetSystemDataAction:", errorMessage);
        return { success: false, message: `Reset failed: ${errorMessage}` };
    }
}

export async function resetMarketingDataAction() {
  try {
    const marketingCollections = [
      'marketingAutomationCampaigns', 'leadScores', 'contentIntelligenceItems',
      'digitalAssets', 'digitalAssetFolders', 'marketingAutomationMetrics',
      'contentTemplates', 'monitoringStreams', 'socialMentions',
      'contentApprovalWorkflows', 'contentApprovals', 'engagementMetrics',
      'campaignMetrics', 'marketing_materials'
    ];

    console.log("Resetting marketing data collections...");
    for (const collectionName of marketingCollections) {
      console.log(`Deleting collection: ${collectionName}...`);
      await deleteCollection(collectionName);
      console.log(`Collection ${collectionName} deleted.`);
    }

    revalidatePath('/marketing');
    revalidatePath('/marketing/automation');
    revalidatePath('/marketing/content-intelligence');
    revalidatePath('/marketing/digital-assets');
    revalidatePath('/marketing/analytics');

    return { success: true, message: `Successfully reset ${marketingCollections.length} marketing collections.` };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error during marketing data reset';
    console.error('Error resetting marketing data:', errorMessage);
    return { success: false, message: `Marketing reset failed: ${errorMessage}` };
  }
}

export async function resetAllUserFacingDataAction() {
  // Import data layer configuration
  const { DATA_CONFIG } = await import('@/lib/dataMode');

  // Allow in development mode (with or without admin controls for testing)
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (!isDevelopment && !DATA_CONFIG.SHOW_ADMIN_CONTROLS) {
    return {
      success: false,
      message: 'Data reset is only available in development mode with admin controls enabled.'
    };
  }

  try {
    const allUserFacingCollections = [
      // Marketing collections
      'marketingAutomationCampaigns', 'leadScores', 'contentIntelligenceItems',
      'digitalAssets', 'digitalAssetFolders', 'marketingAutomationMetrics',
      'contentTemplates', 'monitoringStreams', 'socialMentions',
      'contentApprovalWorkflows', 'contentApprovals', 'engagementMetrics',
      'campaignMetrics', 'marketing_materials', 'marketingCampaignPerformance',
      'marketingChannelPerformance', 'marketingROIData', 'marketingAttributionData',

      // Travel collections
      'travelEntries', 'flightBookings', 'hotelBookings', 'passportInfos', 'visaInfos',
      'perDiemRequests', 'automatedBookings', 'bookingIntegrations', 'travelCurrencies',
      'travelPolicies', 'travelDocuments', 'travel_entries', 'travelCompliance',
      'travelSpendAnalytics', 'policyComplianceMonitoring', 'travelerSafetyTracking',
      'travelOptimizationRecommendations', 'travelUpdates', 'expenses',

      // Procurement collections
      'procurementMetrics', 'vendorMatches', 'rfqsGenerated', 'contractsAnalyzed',
      'riskAssessments', 'purchase_requests', 'purchase_orders', 'vendors',

      // Logistics collections
      'biometricAccess', 'custodyRecords', 'securityMetrics', 'shipments',
      'logistics_optimization', 'supply_chain_analytics', 'warehouse_data',
      'aiSecurityMonitoring', 'complianceReports', 'supplyChainOptimization',
      'supplyChainRiskAssessment', 'supplyChainIntelligence', 'supplyChainBottlenecks',
      'supplyChainOptimizationOpportunities',

      // Workflow collections
      'workflow_analytics', 'workflow_performance', 'automation_metrics',

      // Social media collections
      'social_posts', 'platform_analytics', 'social_campaigns', 'social_performance_metrics',

      // HR Attendance collections
      'booth_attendance', 'business_trip_status', 'attendance_alerts'
    ];

    console.log("Resetting all user-facing data collections...");
    for (const collectionName of allUserFacingCollections) {
      console.log(`Deleting collection: ${collectionName}...`);
      await deleteCollection(collectionName);
      console.log(`Collection ${collectionName} deleted.`);
    }

    // Revalidate all affected paths
    revalidatePath('/travel');
    revalidatePath('/procurement');
    revalidatePath('/logistics');
    revalidatePath('/workflow-hub');
    revalidatePath('/marketing');
    revalidatePath('/smm');

    return { success: true, message: `Successfully reset ${allUserFacingCollections.length} user-facing collections.` };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error during comprehensive data reset';
    console.error('Error resetting all user-facing data:', errorMessage);
    return { success: false, message: `Comprehensive reset failed: ${errorMessage}` };
  }
}

export async function seedMarketingDataAction() {
  // 🚨 DISABLED: This chaotic seeding mechanism has been disabled
  // Use Professional Data Manager instead
  return {
    success: false,
    message: 'This seeding mechanism has been disabled. Use Professional Data Manager instead.',
    redirectTo: 'Professional Data Manager'
  };

  try {
    const marketingCollections = [
      { name: 'marketing_materials', data: mock_marketing_materials },
      { name: 'email_campaigns', data: mock_email_campaigns },
      { name: 'media_contacts', data: mock_media_contacts },
      { name: 'press_kits', data: mock_press_kits },
      { name: 'pitches', data: mock_pitches },
      { name: 'media_events', data: mock_media_events },
      { name: 'media_coverage', data: mock_media_coverages }
    ];

    console.log("Seeding marketing data collections...");
    for (const { name, data } of marketingCollections) {
      console.log(`Seeding collection: ${name} with ${data.length} items...`);
      for (const item of data) {
        await setDocumentWithId(name, item.id, item);
      }
      console.log(`Collection ${name} seeded successfully.`);
    }

    revalidatePath('/marketing');
    revalidatePath('/marketing/automation');
    revalidatePath('/marketing/content-intelligence');
    revalidatePath('/marketing/digital-assets');
    revalidatePath('/marketing/analytics');

    return { success: true, message: `Successfully seeded ${marketingCollections.length} marketing collections.` };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error during marketing data seeding';
    console.error('Error seeding marketing data:', errorMessage);
    return { success: false, message: `Marketing seeding failed: ${errorMessage}` };
  }
}

