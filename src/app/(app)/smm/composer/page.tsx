
"use client";

import React, { useEffect, useState, use, useCallback } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Loader2, <PERSON><PERSON><PERSON>, <PERSON>, Linkedin, Twitter, Facebook, Instagram, Share2, CalendarIcon, Clock, Library, Hash, User, FileText, Image as ImageIcon, Users, Target, Zap, Brain } from "lucide-react";
import { socialPostFormSchema, type SocialPostFormData } from '../schemas';
import { saveSocialPostAction, getActivitiesForSelect, getSocialPostByIdAction, getSmmSettingsAction } from '../actions';
// import { generateSocialPost, type GenerateSocialPostInput } from '@/ai/flows/generate-social-post-flow';
// import { generatePostImage } from '@/ai/flows/generate-post-image-flow';
import type { SocialPlatform, SocialPost, SMMSettings } from '@/types/firestore';

// Additional UI Components
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import ContentTemplateLibrary from '../components/ContentTemplateLibrary';
import ContentApprovalWorkflow from '../components/ContentApprovalWorkflow';
import PlatformOptimizer from '../components/PlatformOptimizer';
import AutoContentAdapter from '../components/AutoContentAdapter';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useSearchParams, useRouter } from 'next/navigation';
import AssetLibraryDialog from '../components/AssetLibraryDialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import NextImage from "next/image";

const platformDetails: Record<SocialPlatform, { name: string, icon: React.ElementType }> = {
  linkedin: { name: "LinkedIn", icon: Linkedin },
  twitter: { name: "X / Twitter", icon: Twitter },
  facebook: { name: "Facebook", icon: Facebook },
  instagram: { name: "Instagram", icon: Instagram },
};

export default function SocialComposerPage() {
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();
  const editPostId = searchParams.get('edit');
  const isEditing = !!editPostId;

  const [activities, setActivities] = useState<{ id: string, name: string, type: 'Exhibition' | 'Event' }[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingAiText, setIsGeneratingAiText] = useState(false);
  const [isGeneratingAiImage, setIsGeneratingAiImage] = useState(false);
  const [activeTab, setActiveTab] = useState<SocialPlatform | undefined>(undefined);
  const [showScheduler, setShowScheduler] = useState(false);
  const [isAssetLibraryOpen, setIsAssetLibraryOpen] = useState(false);
  const [hashtagGroups, setHashtagGroups] = useState<SMMSettings['hashtagGroups']>([]);

  // New state for enhanced features
  const [isTemplateLibraryOpen, setIsTemplateLibraryOpen] = useState(false);
  const [showApprovalWorkflow, setShowApprovalWorkflow] = useState(false);
  const [showPlatformOptimizer, setShowPlatformOptimizer] = useState(true);
  const [showAutoAdapter, setShowAutoAdapter] = useState(false);

  const [currentPostId, setCurrentPostId] = useState<string | undefined>(editPostId || undefined);

  const methods = useForm<SocialPostFormData>({
    resolver: zodResolver(socialPostFormSchema),
    defaultValues: {
      platforms: [], linkedActivityId: '', commonText: '',
      content: { linkedin: { text: '' }, twitter: { text: '' }, facebook: { text: '' }, instagram: { text: '' } },
      publishAt: undefined,
    },
  });

  const selectedPlatforms = methods.watch('platforms');
  const linkedActivityId = methods.watch('linkedActivityId');

  const fetchPostForEditing = useCallback(async (postId: string) => {
    setIsLoading(true);
    try {
        const post = await getSocialPostByIdAction(postId);
        if (post) {
            const initialContent = {
                linkedin: { text: post.content.linkedin?.text || '', imageUrl: post.content.linkedin?.imageUrl || '' },
                twitter: { text: post.content.twitter?.text || '', imageUrl: post.content.twitter?.imageUrl || '' },
                facebook: { text: post.content.facebook?.text || '', imageUrl: post.content.facebook?.imageUrl || '' },
                instagram: { text: post.content.instagram?.text || '', imageUrl: post.content.instagram?.imageUrl || '' },
            };
            methods.reset({
                id: post.id,
                platforms: Object.keys(post.content) as SocialPlatform[],
                linkedActivityId: post.linkedActivity.id,
                content: initialContent,
                publishAt: post.publishAt ? new Date(post.publishAt as string) : undefined
            });
            if(post.publishAt && post.status !== 'draft') {
                setShowScheduler(true);
            }
        } else {
             toast({ title: "Error", description: "Post not found.", variant: "destructive" });
             router.push('/smm');
        }
    } catch(e) {
        toast({ title: "Error", description: "Failed to load post for editing.", variant: "destructive" });
    }
    setIsLoading(false);
  }, [methods, toast, router]);

  useEffect(() => {
    async function fetchInitialData() {
      setIsLoading(true);
      try {
        const [fetchedActivities, smmSettings] = await Promise.all([
          getActivitiesForSelect(),
          getSmmSettingsAction()
        ]);
        setActivities(fetchedActivities);
        setHashtagGroups(smmSettings?.hashtagGroups || []);
      } catch (e) {
        toast({ title: "Error", description: "Could not load Exhibitions/Events.", variant: "destructive" });
      }
      setIsLoading(false);
    }
    fetchInitialData();

    if(isEditing && editPostId) {
        fetchPostForEditing(editPostId);
    }
  }, [toast, isEditing, editPostId, fetchPostForEditing]);

  useEffect(() => {
    if (!activeTab && selectedPlatforms.length > 0) {
      setActiveTab(selectedPlatforms[0]);
    } else if (activeTab && !selectedPlatforms.includes(activeTab)) {
      setActiveTab(selectedPlatforms.length > 0 ? selectedPlatforms[0] : undefined);
    }
  }, [selectedPlatforms, activeTab]);

  const handleInsertText = (text: string, platform?: SocialPlatform) => {
    const targetPlatform = platform || activeTab;
    if (!targetPlatform) return;
    const currentText = methods.getValues(`content.${targetPlatform}.text`) || '';
    methods.setValue(`content.${targetPlatform}.text`, `${currentText}${currentText ? '\n\n' : ''}${text}`);
  };
  
  const handleAiImage = async () => {
    if (!activeTab) return;
    const currentText = methods.getValues(`content.${activeTab}.text`) || methods.getValues(`commonText`);
    if (!currentText || currentText.trim().length < 10) {
        toast({ title: "More Text Needed", description: "Please write some post content first (at least 10 characters) to give the AI context for image generation." });
        return;
    }
    setIsGeneratingAiImage(true);
    try {
        // const result = await generatePostImage({ prompt: currentText });
        // if (result.imageUrl) {
        //     methods.setValue(`content.${activeTab}.imageUrl`, result.imageUrl, { shouldDirty: true });
        //     toast({ title: "AI Image Generated", description: `Image created and added to the ${platformDetails[activeTab].name} post.` });
        // } else {
        //      throw new Error("AI did not return an image.");
        // }
        toast({ title: "AI Image Generation", description: "AI functionality temporarily disabled.", variant: "destructive" });
    } catch(e) {
        toast({ title: "AI Image Failed", description: "Could not generate an image.", variant: "destructive" });
    }
    setIsGeneratingAiImage(false);
  };

  const handleAiDraft = async () => {
    if (!activeTab) return;
    const linkedActivityId = methods.getValues("linkedActivityId");
    if (!linkedActivityId) {
        toast({ title: "Action Required", description: "Please link to an Exhibition or Event before drafting with AI.", variant: "default" });
        return;
    }
    const activity = activities.find(a => a.id === linkedActivityId);
    if (!activity) return;

    setIsGeneratingAiText(true);
    try {
        // const input: GenerateSocialPostInput = {
        //     linkedActivityName: activity.name, linkedActivityType: activity.type, socialPlatform: activeTab,
        // };
        // const result = await generateSocialPost(input);
        // handleInsertText(result.postText, activeTab);
        // toast({ title: "AI Draft Generated", description: `Draft created for ${platformDetails[activeTab].name}.` });
        toast({ title: "AI Text Generation", description: "AI functionality temporarily disabled.", variant: "destructive" });
    } catch(e) {
        toast({ title: "AI Draft Failed", description: "Could not generate text.", variant: "destructive" });
    }
    setIsGeneratingAiText(false);
  };

  const handleSave = async (options: { queuePost?: boolean; submitForApproval?: boolean; publishNow?: boolean; }) => {
    setIsSubmitting(true);
    const data = methods.getValues();
    const result = await saveSocialPostAction(data, options);
    setIsSubmitting(false);

    if (result.errors) {
        toast({ title: "Error", description: result.message || "Failed to save post.", variant: "destructive"});
    } else {
        toast({ title: "Success", description: result.message });
        router.push('/smm');
    }
  };

  const activeTabImageUrl = activeTab ? methods.watch(`content.${activeTab}.imageUrl`) : undefined;

  // Handler for template selection
  const handleTemplateSelect = (template: any) => {
    // Apply template content to form
    selectedPlatforms.forEach(platform => {
      if (template.content[platform]) {
        methods.setValue(`content.${platform}.text`, template.content[platform].text);
      }
    });

    toast({
      title: "Template Applied",
      description: `"${template.name}" template has been applied to your content.`
    });
  };



  // Get current post content for brand checker
  const getCurrentPostContent = () => {
    const content: Record<SocialPlatform, { text: string; imageUrl?: string }> = {} as any;
    selectedPlatforms.forEach(platform => {
      content[platform] = {
        text: methods.watch(`content.${platform}.text`) || '',
        imageUrl: methods.watch(`content.${platform}.imageUrl`)
      };
    });
    return content;
  };

  // Handler for platform optimization
  const handleContentOptimize = (platform: SocialPlatform, optimizedContent: string) => {
    methods.setValue(`content.${platform}.text`, optimizedContent);
    toast({
      title: "Content Optimized",
      description: `Content optimized for ${platform}.`
    });
  };

  const handleScheduleOptimize = (platform: SocialPlatform, optimalTime: string) => {
    // Set optimal time for scheduling
    const today = new Date();
    const [hours, minutes] = optimalTime.split(':').map(Number);
    today.setHours(hours, minutes, 0, 0);
    methods.setValue('publishAt', today);
    setShowScheduler(true);
    toast({
      title: "Schedule Optimized",
      description: `Optimal posting time set for ${platform}.`
    });
  };

  // Handler for auto content adaptation
  const handleAdaptedContent = (adaptations: Record<SocialPlatform, string>) => {
    Object.entries(adaptations).forEach(([platform, content]) => {
      methods.setValue(`content.${platform as SocialPlatform}.text`, content);
    });
    toast({
      title: "Content Adapted",
      description: "Content has been automatically adapted for all platforms."
    });
  };

  // Get base content for auto adapter
  const getBaseContent = () => {
    const commonText = methods.watch('commonText');
    if (commonText) return commonText;

    // Use the first platform's content as base
    const firstPlatform = selectedPlatforms[0];
    return firstPlatform ? methods.watch(`content.${firstPlatform}.text`) || '' : '';
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <Share2 className="mr-3 h-8 w-8 text-primary" />
            Social Media Composer
          </h1>
          <p className="text-muted-foreground">
            {isEditing ? 'Edit your social post.' : 'Draft and publish posts to your connected social media channels.'}
          </p>
        </div>
      </div>

      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleSave)} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Post Setup</CardTitle>
                <CardDescription>Configure your post settings and target platforms</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={methods.control}
                  name="linkedActivityId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Link to Exhibition or Event *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={isLoading}>
                        <FormControl><SelectTrigger><SelectValue placeholder={isLoading ? "Loading activities..." : "Select an activity"} /></SelectTrigger></FormControl>
                        <SelectContent>
                          {activities.map(act => (
                            <SelectItem key={act.id} value={act.id}>{act.name} ({act.type})</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control} name="platforms"
                  render={() => (
                    <FormItem>
                      <FormLabel>Publish to Channels *</FormLabel>
                      <div className="flex flex-wrap gap-4 pt-2">
                        {Object.keys(platformDetails).map((platformId) => {
                          const platform = platformDetails[platformId as SocialPlatform];
                          const PlatformIcon = platform.icon;
                          return (
                            <FormField
                              key={platformId} control={methods.control} name="platforms"
                              render={({ field }) => (
                                <FormItem className="flex items-center space-x-2 space-y-0">
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(platformId as SocialPlatform)}
                                      onCheckedChange={(checked) => {
                                        const newValue = checked ? [...(field.value || []), platformId as SocialPlatform] : (field.value || []).filter((value) => value !== platformId);
                                        field.onChange(newValue);
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="font-normal flex items-center gap-1.5 cursor-pointer">
                                    <PlatformIcon className="h-4 w-4" /> {platform.name}
                                  </FormLabel>
                                </FormItem>
                              )}
                            />
                          );
                        })}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {selectedPlatforms.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Content Composer</CardTitle>
                  <CardDescription>Create engaging content optimized for each platform</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap justify-end gap-2 mb-4">
                    <Button type="button" variant="outline" size="sm" onClick={() => setIsTemplateLibraryOpen(true)}>
                      <FileText className="mr-2 h-4 w-4" /> Templates
                    </Button>
                    <Button type="button" variant="outline" size="sm" onClick={handleAiDraft} disabled={isGeneratingAiText || !activeTab}>
                      {isGeneratingAiText ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <Sparkles className="mr-2 h-4 w-4" />}
                      AI Draft Text
                    </Button>
                    <Button type="button" variant="outline" size="sm" onClick={handleAiImage} disabled={isGeneratingAiImage || !activeTab}>
                      {isGeneratingAiImage ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <ImageIcon className="mr-2 h-4 w-4" />}
                      AI Generate Image
                    </Button>
                  </div>
                  <FormField
                    control={methods.control} name="commonText"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Common Text (Optional)</FormLabel>
                        <FormControl><Textarea {...field} rows={4} placeholder="Write a general message here. You can then customize it for each platform below."/></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as SocialPlatform)} className="w-full mt-4">
                    <TabsList>
                      {selectedPlatforms.map(platformId => (
                        <TabsTrigger key={platformId} value={platformId}>
                          {React.createElement(platformDetails[platformId].icon, { className: "mr-2 h-4 w-4" })}
                          {platformDetails[platformId].name}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                    {selectedPlatforms.map(platformId => (
                      <TabsContent key={platformId} value={platformId} className="mt-4">
                        {activeTab === platformId && activeTabImageUrl && (
                          <div className="mb-4 relative w-full h-64 bg-muted rounded-md overflow-hidden">
                            <NextImage src={activeTabImageUrl} alt="Generated post image" fill className="object-cover" />
                          </div>
                        )}
                        <FormField
                          control={methods.control} name={`content.${platformId}.text`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Custom text for {platformDetails[platformId].name}</FormLabel>
                              <FormControl><Textarea {...field} rows={6} placeholder={`Write your post for ${platformDetails[platformId].name}...`}/></FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TabsContent>
                    ))}
                  </Tabs>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Publishing Options</CardTitle>
                <CardDescription>Configure when and how your post will be published</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch id="scheduler-switch" checked={showScheduler} onCheckedChange={setShowScheduler} />
                  <Label htmlFor="scheduler-switch">Schedule for later</Label>
                </div>
                {showScheduler && (
                  <FormField
                    control={methods.control} name="publishAt"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Publish Date & Time</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button variant={"outline"} className={cn("w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground")}>
                                {field.value ? format(new Date(field.value), "PPP p") : <span>Pick a date and time</span>}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))} />
                            <div className="p-2 border-t">
                              <input type="time" className="w-full p-1 border rounded"
                                defaultValue={field.value ? format(new Date(field.value), "HH:mm") : ''}
                                onChange={(e) => {
                                  const [hours, minutes] = e.target.value.split(':').map(Number);
                                  const newDate = field.value || new Date();
                                  newDate.setHours(hours, minutes);
                                  field.onChange(newDate);
                                }} />
                            </div>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>
            <div className="flex justify-end sticky bottom-0 py-4 bg-background/80 backdrop-blur-sm">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {isEditing ? 'Updating...' : 'Publishing...'}
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    {isEditing ? 'Update Post' : (showScheduler ? 'Schedule Post' : 'Publish Now')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </FormProvider>

        {/* Platform Optimizer */}
        {showPlatformOptimizer && selectedPlatforms.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Platform Optimization
            </CardTitle>
            <CardDescription>
              Optimize your content for maximum engagement on each platform.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PlatformOptimizer
              postContent={getCurrentPostContent()}
              platforms={selectedPlatforms}
              onContentOptimize={handleContentOptimize}
              onScheduleOptimize={handleScheduleOptimize}
            />
          </CardContent>
        </Card>
      )}

      {/* Auto Content Adapter */}
      {showAutoAdapter && selectedPlatforms.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Automatic Content Adaptation
            </CardTitle>
            <CardDescription>
              Automatically adapt your content for each platform's best practices.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AutoContentAdapter
              baseContent={getBaseContent()}
              platforms={selectedPlatforms}
              onAdaptedContent={handleAdaptedContent}
            />
          </CardContent>
        </Card>
      )}

      {/* Content Approval Workflow */}
      {(currentPostId || isEditing) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Content Approval
            </CardTitle>
            <CardDescription>
              Manage approval workflow for this content.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ContentApprovalWorkflow
              postId={currentPostId}
              postContent={getCurrentPostContent()}
              onApprovalStatusChange={(status) => {
                // Handle approval status changes
                console.log('Approval status changed:', status);
              }}
            />
          </CardContent>
        </Card>
        )}

        <AssetLibraryDialog
          isOpen={isAssetLibraryOpen}
          setIsOpen={setIsAssetLibraryOpen}
          onTextInsert={handleInsertText}
          linkedActivityId={linkedActivityId}
        />

        <ContentTemplateLibrary
          isOpen={isTemplateLibraryOpen}
          setIsOpen={setIsTemplateLibraryOpen}
          onTemplateSelect={handleTemplateSelect}
        />
      </div>
    </>
  );
}
