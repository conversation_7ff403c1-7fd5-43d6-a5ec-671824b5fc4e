
"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { AppSidebar } from "@/components/layout/app-sidebar";
import { AppHeader } from "@/components/layout/app-header";
import {
  SidebarProvider,
  SidebarInset,
  Sidebar,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/contexts/auth-context";
import { EvexIcon } from "@/components/icons/evex-icon";
import { CommandPaletteProvider } from "@/contexts/command-palette-context";
import CommandPalette from "@/components/layout/command-palette";
import { FocusModeProvider, useFocusMode } from "@/contexts/focus-mode-context";
import { AssistantContextProvider } from "@/contexts/assistant-context";
import { AssistantFAB } from "@/components/assistant/AssistantFAB";
import { QuickActions } from "@/components/assistant/QuickActions";
import { VoiceControl } from "@/components/assistant/VoiceControl";
import { FloatingHelp } from "@/components/assistant/ContextualHelp";
import { KeyboardShortcutsProvider } from "@/components/ui/KeyboardShortcutsDialog";
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { evexanaAnalyticsService } from "@/services/evexanaAnalyticsService";
import { PerformanceMonitor } from "@/components/debug/PerformanceMonitor";
import { preloadCommonData } from "@/services/optimizedFirestoreService";
import { PWAInstallPrompt, usePWAInstall } from "@/components/mobile/PWAInstallPrompt";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { useTheme } from "@/lib/theme";
import { cn } from "@/lib/utils";

// Inner component to use useFocusMode after FocusModeProvider
function MainAppContent({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const { isFocusMode } = useFocusMode(); // Correctly consume context here
  const { showPrompt, hidePrompt, dismissPrompt } = usePWAInstall();

  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  // Initialize analytics service and preload data
  React.useEffect(() => {
    evexanaAnalyticsService.loadPersistedData();

    // Preload common data for better performance
    if (isAuthenticated) {
      preloadCommonData();
    }
  }, [isAuthenticated]);

  React.useEffect(() => {
    console.log('Auth state:', { isLoading, isAuthenticated, user });

    // STRICT AUTHENTICATION - NO BYPASSES ALLOWED
    if (!isLoading && !isAuthenticated) {
      console.log('Redirecting to login - user not authenticated');
      router.push("/login");
    }
  }, [isLoading, isAuthenticated, router, user]);

  // Development bypass check
  const isDevelopment = process.env.NODE_ENV === 'development';

  // In development, always allow access
  if (isDevelopment) {
    // Skip loading screen in development
  } else if (isLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <EvexIcon className="h-16 w-16 text-primary animate-pulse" />
          <p className="text-muted-foreground">Loading EVEXA...</p>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <AppSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <AppHeader />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8 bg-background">
            {children}
          </main>
        </div>
      </div>
      <AssistantFAB />
      <QuickActions />
      <VoiceControl />
      <FloatingHelp />
      <PWAInstallPrompt />
      <PerformanceMonitor />
    </SidebarProvider>
  );
}

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultMode="dark" defaultVariant="professional">
      <FocusModeProvider> {/* FocusModeProvider wraps CommandPaletteProvider and MainAppContent */}
        <CommandPaletteProvider>
          <AssistantContextProvider>
            <KeyboardShortcutsProvider>
              <ThemedMainAppContent>{children}</ThemedMainAppContent>
              <CommandPalette />
            </KeyboardShortcutsProvider>
          </AssistantContextProvider>
        </CommandPaletteProvider>
      </FocusModeProvider>
    </ThemeProvider>
  );
}

function ThemedMainAppContent({ children }: { children: React.ReactNode }) {
  const { variant, effectiveMode } = useTheme();

  // Apply theme classes to body as well
  React.useEffect(() => {
    const body = document.body;
    // Remove existing theme classes
    body.classList.remove('theme-default', 'theme-corporate', 'theme-modern', 'theme-minimal', 'theme-vibrant', 'theme-professional');
    body.classList.remove('light', 'dark');

    // Validate variant name before adding to classList (prevent DOMTokenList errors)
    const safeVariant = variant.replace(/\s+/g, '-').toLowerCase();
    if (safeVariant && !safeVariant.includes(' ')) {
      // Add current theme classes
      body.classList.add(`theme-${safeVariant}`, effectiveMode);
    } else {
      console.warn('Invalid theme variant detected in app layout, using fallback:', variant);
      body.classList.add('theme-gold', effectiveMode); // fallback
    }
  }, [variant, effectiveMode]);

  // Validate variant for className usage
  const safeVariant = variant.replace(/\s+/g, '-').toLowerCase();
  const themeClass = safeVariant && !safeVariant.includes(' ') ? `theme-${safeVariant}` : 'theme-gold';

  return (
    <div className={cn(
      "min-h-screen",
      themeClass,
      effectiveMode
    )}>
      <MainAppContent>{children}</MainAppContent>
    </div>
  );
}
