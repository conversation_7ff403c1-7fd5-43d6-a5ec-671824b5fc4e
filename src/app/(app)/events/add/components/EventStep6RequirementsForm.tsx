
"use client";

import { useFormContext } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import type { EventFormData } from "../../actions";
import { Utensils, Tv2 } from "lucide-react";

export default function EventStep6RequirementsForm() {
  const { control } = useFormContext<EventFormData>();

  return (
    <div className="space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-foreground">Event Requirements</h2>
        <p className="text-muted-foreground">Detail any specific logistical, technical, or catering needs for this event</p>
      </div>

      <div className="space-y-6">
        <FormField
          control={control}
          name="cateringRequirements"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced flex items-center">
                <Utensils className="h-4 w-4 mr-2"/>
                Catering Requirements
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Coffee and pastries on arrival. Lunch for 25 people with vegetarian and gluten-free options. List of dietary restrictions..."
                  className="textarea-enhanced min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="avRequirements"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced flex items-center">
                <Tv2 className="h-4 w-4 mr-2"/>
                AV & Technical Requirements
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., 1x Projector with HDMI input, 2x wireless handheld microphones, 1x speaker podium, stable Wi-Fi for live streaming to YouTube."
                  className="textarea-enhanced min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
