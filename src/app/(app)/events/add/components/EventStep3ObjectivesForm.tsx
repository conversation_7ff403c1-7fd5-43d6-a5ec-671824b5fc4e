
"use client";

import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import type { EventFormData } from "../../actions";
import { Target, TrendingUp, CheckCircle, Link } from "lucide-react";

export default function EventStep3ObjectivesForm() {
  const { control } = useFormContext<EventFormData>();

  return (
    <div className="form-container-enhanced">
      {/* Primary Objective */}
      <div className="form-field-card-primary">
        <FormField
          control={control}
          name="primaryObjective"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-section-header form-section-header-primary">
                <div className="form-section-header-icon">
                  <Target className="h-5 w-5" />
                </div>
                Primary Objective
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., To finalize the Q4 marketing strategy and assign key responsibilities."
                  className="textarea-enhanced min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Secondary Objectives */}
      <div className="form-field-card">
        <FormField
          control={control}
          name="secondaryObjectives"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-section-header form-section-header-blue">
                <div className="form-section-header-icon">
                  <TrendingUp className="h-5 w-5" />
                </div>
                Secondary Objectives
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Team building, knowledge sharing on new tools."
                  className="textarea-enhanced min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Success Metrics */}
      <div className="form-field-card-success">
        <FormField
          control={control}
          name="successMetrics"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-section-header form-section-header-green">
                <div className="form-section-header-icon">
                  <CheckCircle className="h-5 w-5" />
                </div>
                Success Metrics
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., 90% task completion rate by EOD, all participants clear on their roles."
                  className="textarea-enhanced min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Target Audience & Attendance */}
      <div className="form-field-card-secondary space-y-6">
        <div className="form-section-header form-section-header-blue">
          <div className="form-section-header-icon">
            <Target className="h-5 w-5" />
          </div>
          <h3>Target Audience & Attendance</h3>
        </div>

        <FormField
          control={control}
          name="targetAudience"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Target Audience / Attendees</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Marketing Department Heads, Project Managers, Sales Representatives."
                  className="textarea-enhanced min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormDescription className="form-description-enhanced">List the primary groups or individuals this event is for.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="estimatedAttendance"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Estimated Attendance</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="e.g., 25"
                  className="input-enhanced"
                  {...field}
                  onChange={e => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Feedback Survey */}
      <div className="form-field-card space-y-4">
        <div className="form-section-header form-section-header-primary">
          <div className="form-section-header-icon">
            <Link className="h-5 w-5" />
          </div>
          <h3>Post-Event Feedback Collection</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="p-4 border rounded-lg bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <h4 className="font-medium text-sm">Built-in EVEXA Feedback System ✨</h4>
              </div>
              <p className="text-xs text-muted-foreground mb-3">
                EVEXA's integrated real-time feedback collection with AI-powered analytics, sentiment analysis, and automated reporting.
              </p>
              <div className="space-y-2 mb-3">
                <div className="flex items-center gap-2 text-xs text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>Real-time feedback collection during events</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>AI sentiment analysis & trending topics</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>Automated analytics & reporting</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>Live feedback dashboard for organizers</span>
                </div>
              </div>
              <div className="flex items-center gap-2 text-xs font-medium text-primary">
                <span className="px-2 py-1 bg-primary/10 rounded-full">✅ IMPLEMENTED</span>
                <span>Ready to use - No setup required</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <FormField
              control={control}
              name="feedbackSurveyUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="form-label-enhanced text-sm">
                    External Survey URL (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://forms.gle/your-survey"
                      className="input-enhanced"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="form-description-enhanced text-xs">
                    Link to external survey if you prefer using Google Forms, SurveyMonkey, etc.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
