
"use client";

import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { DollarSign, CreditCard, FileText, AlertCircle } from "lucide-react";
import { CURRENCY_OPTIONS, FUNDING_SOURCE_OPTIONS } from "@/lib/constants";
import type { EventFormData } from "../../actions";
import { useState, useEffect } from "react";
import { getFundingSourceDefinitionsAction } from "@/app/(app)/settings/actions";
import type { FundingSourceDefinition } from "@/types/firestore";

export default function EventStep4BudgetForm() {
  const { control } = useFormContext<EventFormData>();
  const [fundingSources, setFundingSources] = useState<FundingSourceDefinition[]>([]);
  const [isLoadingFundingSources, setIsLoadingFundingSources] = useState(true);

  useEffect(() => {
    const loadFundingSources = async () => {
      try {
        const sources = await getFundingSourceDefinitionsAction();
        setFundingSources(sources);

        if (sources.length === 0) {
          console.info('No funding sources found. User may need to create them in Settings.');
        }
      } catch (error) {
        console.error('Error loading funding sources:', error);
        setFundingSources([]);
        // Don't show error toast here as it might be due to no data rather than an error
      } finally {
        setIsLoadingFundingSources(false);
      }
    };

    loadFundingSources();
  }, []);

  return (
    <div className="form-container-enhanced">
      {/* Budget & Currency */}
      <div className="form-field-card-primary">
        <div className="form-section-header form-section-header-primary">
          <div className="form-section-header-icon">
            <DollarSign className="h-5 w-5" />
          </div>
          <h3>Budget Information</h3>
        </div>

        <div className="form-grid-2">
          <FormField
            control={control}
            name="estimatedBudget"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="form-label-enhanced">Estimated Budget *</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="e.g., 5000"
                    className="input-enhanced"
                    {...field}
                    onChange={e => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="form-label-enhanced">Currency *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value || ''}>
                  <FormControl>
                    <SelectTrigger className="select-trigger-enhanced">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {CURRENCY_OPTIONS.map(currency => (
                      <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="form-description-enhanced">
                  Multi-currency support for global events
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Funding Source */}
      <div className="form-field-card">
        <div className="form-section-header form-section-header-blue">
          <div className="form-section-header-icon">
            <CreditCard className="h-5 w-5" />
          </div>
          <h3>Funding Source</h3>
        </div>

        <div className="space-y-4">
          {isLoadingFundingSources ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span>Loading funding sources...</span>
            </div>
          ) : fundingSources.length > 0 ? (
            <FormField
              control={control}
              name="fundingSource"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="form-label-enhanced">Select Funding Source *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value || ''}>
                    <FormControl>
                      <SelectTrigger className="select-trigger-enhanced">
                        <SelectValue placeholder="Choose from configured funding sources" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {fundingSources.map(source => (
                        <SelectItem key={source.id} value={source.name}>
                          <div className="flex flex-col">
                            <span>{source.name}</span>
                            {source.description && (
                              <span className="text-xs text-muted-foreground">{source.description}</span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription className="form-description-enhanced">
                    Funding sources are configured in Settings → Data Configuration
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-orange-800">No Funding Sources Configured</h4>
                  <p className="text-sm text-orange-700 mt-1">
                    Please configure funding sources in Settings → Data Configuration before creating events.
                  </p>
                  <FormField
                    control={control}
                    name="fundingSource"
                    render={({ field }) => (
                      <FormItem className="mt-3">
                        <FormLabel className="form-label-enhanced text-sm">Manual Entry (Temporary)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Marketing Department Budget"
                            className="input-enhanced"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Budget Notes & Tracking */}
      <div className="form-field-card-secondary space-y-6">
        <div className="form-section-header form-section-header-blue">
          <div className="form-section-header-icon">
            <FileText className="h-5 w-5" />
          </div>
          <h3>Budget Notes & Tracking</h3>
        </div>

        <FormField
          control={control}
          name="budgetNotes"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Budget Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Includes catering and materials. Travel expenses are separate."
                  className="textarea-enhanced"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="actualSpending"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Actual Spending (Auto-calculated)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="0.00"
                  className="input-enhanced"
                  {...field}
                  disabled
                />
              </FormControl>
              <FormDescription className="form-description-enhanced">
                This field automatically updates from linked expenses in the Financial module.
                All event expenses will be tracked and reflected here in real-time.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-3">
            <FileText className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800">Financial Integration</h4>
              <p className="text-sm text-blue-700 mt-1">
                After creating this event, you can:
              </p>
              <ul className="text-sm text-blue-700 mt-2 space-y-1">
                <li>• Create detailed budgets with financial categories</li>
                <li>• Track expenses against this event</li>
                <li>• Generate financial reports and analytics</li>
                <li>• Monitor spending vs. budget in real-time</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
