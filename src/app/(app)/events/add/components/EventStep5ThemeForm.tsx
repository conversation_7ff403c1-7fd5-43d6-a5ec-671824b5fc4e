
"use client";

import { useForm<PERSON>ontext, useFieldArray } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import type { EventFormData } from "../../actions";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Trash2, Image as ImageIcon } from "lucide-react";
import { FileUpload } from "@/components/ui/file-upload";

export default function EventStep5ThemeForm() {
  const { control, watch } = useFormContext<EventFormData>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "moodboardItems",
  });

  const watchedMoodboardItems = watch("moodboardItems");

  return (
    <div className="form-container-enhanced">
      {/* Theme Description - Full Width */}
      <div className="form-field-card-primary">
        <FormField
          control={control}
          name="themeDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced-large">Event Theme / Concept</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the overall theme or concept of the event. e.g., 'Innovation & Collaboration', 'Future Forward Tech'"
                  className="textarea-enhanced min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Branding Guidelines */}
      <div className="form-field-card">
        <FormField
          control={control}
          name="brandingNotes"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Branding Guidelines & Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Use primary blue (#3F51B5) and accent purple (#7E57C2). Company logo must be prominent on all digital and physical materials. Font: Poppins."
                  className="textarea-enhanced min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormDescription className="form-description-enhanced">
                Key branding elements to consider for event materials, presentations, etc.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Dress Code */}
      <div className="form-field-card">
        <FormField
          control={control}
          name="dressCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Dress Code (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Business Casual, Formal, Casual" className="input-enhanced" {...field} />
              </FormControl>
              <FormDescription className="form-description-enhanced">
                Specify the dress code for attendees.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Mood Board Section */}
      <div className="form-field-card">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-primary" />
              Visual Mood Board
            </h3>
            <FormDescription className="form-description-enhanced mt-1">
              Upload images for visual inspiration (e.g., decor, style, atmosphere). Supports JPG, PNG, WebP formats.
            </FormDescription>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => append({ fileUrl: "", fileName: "", caption: "" })}
            className="button-enhanced flex items-center gap-2 h-9"
          >
            <PlusCircle className="h-4 w-4" />
            Add Image
          </Button>
        </div>

        {fields.map((item, index) => (
          <div key={item.id} className="p-6 border rounded-lg space-y-4 relative bg-gradient-to-br from-background to-muted/30 border-border/50">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-foreground">Inspiration Image #{index + 1}</h4>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => remove(index)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <FormField
              control={control}
              name={`moodboardItems.${index}.fileUrl`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="form-label-enhanced">Upload Inspiration Image *</FormLabel>
                  <FormControl>
                    <FileUpload
                      value={null} // We'll handle file conversion to URL separately
                      onChange={(files) => {
                        // Handle file upload and convert to URL
                        if (files && !Array.isArray(files)) {
                          // For now, create a temporary URL for preview
                          const url = URL.createObjectURL(files);
                          field.onChange(url);
                          // In production, you'd upload to Firebase Storage here
                        }
                      }}
                      accept="image/*"
                      maxSize={5}
                      maxFiles={1}
                      variant="image-only"
                      label="Upload Image"
                      description="Upload an inspiration image (JPG, PNG, WebP - Max 5MB)"
                      showPreview={true}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="form-grid-2">
              <FormField
                control={control}
                name={`moodboardItems.${index}.fileName`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="form-label-enhanced">File Name (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Main Stage Concept" className="input-enhanced" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name={`moodboardItems.${index}.caption`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="form-label-enhanced">Caption (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Color palette inspiration" className="input-enhanced" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        ))}
        {fields.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-border/50 rounded-lg">
            <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground mb-4">No mood board images added yet</p>
            <Button
              type="button"
              variant="outline"
              onClick={() => append({ fileUrl: "", fileName: "", caption: "" })}
              className="button-enhanced"
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Add First Image
            </Button>
          </div>
        )}

        {fields.length > 0 && (
          <Button
            type="button"
            variant="outline"
            onClick={() => append({ fileUrl: "", fileName: "", caption: "" })}
            className="button-enhanced w-full"
          >
            <PlusCircle className="mr-2 h-4 w-4" /> Add Another Image
          </Button>
        )}
      </div>
    </div>
  );
}
