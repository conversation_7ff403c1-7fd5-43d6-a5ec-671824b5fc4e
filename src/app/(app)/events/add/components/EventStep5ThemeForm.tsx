
"use client";

import { useFormContext, useFieldArray } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import type { EventFormData } from "../../actions";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Trash2, Image as ImageIcon } from "lucide-react";
import NextImage from "next/image"; // Using NextImage for potential optimization

export default function EventStep5ThemeForm() {
  const { control, watch } = useFormContext<EventFormData>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "moodboardItems",
  });

  const watchedMoodboardItems = watch("moodboardItems");

  return (
    <div className="space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-foreground">Theme & Visual Identity</h2>
        <p className="text-muted-foreground">Define the visual concept and branding guidelines for your event</p>
      </div>

      <div className="space-y-6">
        <FormField
          control={control}
          name="themeDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Event Theme / Concept</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the overall theme or concept of the event. e.g., 'Innovation & Collaboration', 'Future Forward Tech'"
                  className="textarea-enhanced min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="brandingNotes"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="form-label-enhanced">Branding Guidelines & Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Use primary blue (#3F51B5) and accent purple (#7E57C2). Company logo must be prominent on all digital and physical materials. Font: Poppins."
                  className="textarea-enhanced min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormDescription className="form-description-enhanced">
                Key branding elements to consider for event materials, presentations, etc.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="dressCode"
          render={({ field }) => (
            <FormItem className="pt-4 border-t">
              <FormLabel className="form-label-enhanced">Dress Code (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Business Casual, Formal, Casual" className="input-enhanced" {...field} />
              </FormControl>
              <FormDescription className="form-description-enhanced">
                Specify the dress code for attendees.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="space-y-6 pt-6 border-t">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-primary" />
              Visual Mood Board
            </h3>
            <FormDescription className="form-description-enhanced mt-1">
              Add links to images for visual inspiration (e.g., decor, style, atmosphere). Actual file uploads will be supported later.
            </FormDescription>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => append({ fileUrl: "", fileName: "", caption: "" })}
            className="button-enhanced flex items-center gap-2 h-9"
          >
            <PlusCircle className="h-4 w-4" />
            Add Image
          </Button>
        </div>

        {fields.map((item, index) => (
          <div key={item.id} className="p-6 border rounded-lg space-y-4 relative bg-gradient-to-br from-background to-muted/30 border-border/50">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-foreground">Inspiration Image #{index + 1}</h4>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => remove(index)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <FormField
              control={control}
              name={`moodboardItems.${index}.fileUrl`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="form-label-enhanced">Image URL *</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/image.jpg" className="input-enhanced" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="form-grid-2">
              <FormField
                control={control}
                name={`moodboardItems.${index}.fileName`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="form-label-enhanced">File Name (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Main Stage Concept" className="input-enhanced" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name={`moodboardItems.${index}.caption`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="form-label-enhanced">Caption (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Color palette inspiration" className="input-enhanced" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Image preview */}
            {watchedMoodboardItems?.[index]?.fileUrl && (
              <div className="mt-4 p-3 border rounded-lg bg-background/50">
                <p className="text-sm text-muted-foreground mb-2">Preview:</p>
                <NextImage
                  src={`https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=200&h=100&fit=crop&crop=center`}
                  alt={watchedMoodboardItems?.[index]?.caption || 'Moodboard image preview'}
                  width={200}
                  height={100}
                  className="rounded-md object-cover border"
                  data-ai-hint="moodboard event design"
                />
              </div>
            )}
          </div>
        ))}
        {fields.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-border/50 rounded-lg">
            <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground mb-4">No mood board images added yet</p>
            <Button
              type="button"
              variant="outline"
              onClick={() => append({ fileUrl: "", fileName: "", caption: "" })}
              className="button-enhanced"
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Add First Image
            </Button>
          </div>
        )}

        {fields.length > 0 && (
          <Button
            type="button"
            variant="outline"
            onClick={() => append({ fileUrl: "", fileName: "", caption: "" })}
            className="button-enhanced w-full"
          >
            <PlusCircle className="mr-2 h-4 w-4" /> Add Another Image
          </Button>
        )}
      </div>
    </div>
  );
}
