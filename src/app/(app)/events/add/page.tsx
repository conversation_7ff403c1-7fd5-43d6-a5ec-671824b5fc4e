
"use client";

import * as React from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { addEventAction, type EventFormData } from "../actions";
import Link from "next/link";
import { Progress } from "@/components/ui/progress";
import StepIndicator from "@/app/(app)/exhibitions/onboarding/components/StepIndicator";

// Import Step Components
import EventStep1SetupForm from "./components/EventStep1SetupForm";
import EventStep2DateTimeLocationForm from "./components/EventStep2DateTimeLocationForm";
import EventStep3ObjectivesForm from "./components/EventStep3ObjectivesForm";
import EventStep4BudgetForm from "./components/EventStep4BudgetForm";
import EventStep5ThemeForm from "./components/EventStep5ThemeForm";
import EventStep6RequirementsForm from "./components/EventStep6RequirementsForm";
import EventStep7AssignedVendorsForm from "./components/EventStep7AssignedVendorsForm";
import EventStep8StaffingPlanForm from "./components/EventStep8StaffingPlanForm";
import EventStep9RunOfShowForm from "./components/EventStep9RunOfShowForm";
import EventStep10AttachmentsForm from "./components/EventStep10AttachmentsForm";
import EventStep11CustomFieldsForm from "./components/EventStep11CustomFieldsForm";

const LOCAL_STORAGE_KEY = "eventOnboardingFormData";

// Client-side Zod schema for form structure and basic type validation.
const eventAttachmentSchemaClient = z.object({
  fileName: z.string(),
  fileUrl: z.string().url().or(z.literal('')), 
  fileType: z.string().optional(),
});

const moodboardItemSchemaClient = z.object({
  fileUrl: z.string().url({ message: "Please enter a valid URL for the mood board image."}).min(1, "Image URL is required."),
  fileName: z.string().optional(),
  caption: z.string().optional(),
});

const staffingPlanItemSchemaClient = z.object({
  role: z.string().min(1, "Role is required."),
  assignedToUserId: z.string().optional(),
  assignedToPlaceholder: z.string().optional(),
  shift: z.string().optional(),
  notes: z.string().optional(),
});

const assignedVendorSchemaClient = z.object({
  vendorId: z.string().min(1, "Vendor selection is required."),
  vendorName: z.string(), // Denormalized, set by selection
  roleOrService: z.string().min(1, "Role/Service provided by vendor is required."),
});

const runOfShowItemSchemaClient = z.object({
  time: z.string().min(1, "Time is required."),
  activity: z.string().min(1, "Activity description is required."),
  responsiblePerson: z.string().optional(),
  notes: z.string().optional(),
});


const eventOnboardingSchemaClient = z.object({
  // Step 1
  eventName: z.string().min(1, "Event name is required"),
  eventType: z.string().optional(),
  eventStatus: z.enum(['Planning', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', '']).optional(),
  eventOwner: z.string().optional(),
  coreTeamMembers: z.array(z.string()).default([]),
  parentExhibitionId: z.string().optional(),
  linkedExhibitionId: z.string().optional(),
  privacyLevel: z.enum(['Public', 'Private - Invite Only', 'Internal - Company Only', '']).optional(),

  // Step 2
  locationType: z.enum(['Physical', 'Virtual', 'Hybrid', '']).optional(),
  venueName: z.string().optional(),
  address: z.string().optional(),
  virtualMeetingLink: z.string().url().optional().or(z.literal('')),
  startDate: z.union([z.date(), z.string()]),
  startTime: z.string().default(""),
  endDate: z.union([z.date(), z.string()]),
  endTime: z.string().default(""),
  registrationDeadline: z.date().optional().or(z.string().optional()),
  isRecurring: z.boolean(),
  recurrencePattern: z.string().optional(),
  
  // Step 3
  primaryObjective: z.string().optional(),
  secondaryObjectives: z.string().optional(),
  successMetrics: z.string().optional(),
  targetAudience: z.string().optional(),
  estimatedAttendance: z.number().optional(),
  feedbackSurveyUrl: z.string().url().optional().or(z.literal('')),

  // Step 4
  estimatedBudget: z.number().optional(),
  actualSpending: z.number().optional(),
  currency: z.enum(['USD', 'EUR', 'GBP', '']).optional(),
  fundingSource: z.string().optional(),
  budgetNotes: z.string().optional(),
  
  // Step 5
  themeDescription: z.string().optional(),
  brandingNotes: z.string().optional(),
  moodboardItems: z.array(moodboardItemSchemaClient).optional(),
  dressCode: z.string().optional(),

  // Step 6
  cateringRequirements: z.string().optional(),
  avRequirements: z.string().optional(),
  
  // Step 7 (Vendors)
  assignedVendors: z.array(assignedVendorSchemaClient).optional(),

  // Step 8 (Staffing)
  staffingPlan: z.array(staffingPlanItemSchemaClient).optional(),

  // Step 9 (Run of show)
  runOfShow: z.array(runOfShowItemSchemaClient).optional(),

  // Step 10 (Attachments)
  attachments: z.array(eventAttachmentSchemaClient).optional(),
  
  // Step 11 (Custom Fields)
  customFields: z.record(z.any()).optional(),

}).refine(data => {
    if (data.startDate && data.endDate) {
      const startDate = typeof data.startDate === 'string' ? new Date(data.startDate) : data.startDate;
      const endDate = typeof data.endDate === 'string' ? new Date(data.endDate) : data.endDate;
      if (endDate < startDate) return false;
      if (endDate.getTime() === startDate.getTime()) {
        if (data.startTime && data.endTime && data.startTime !== '' && data.endTime !== '') {
          return data.endTime >= data.startTime;
        }
      }
    }
    return true;
  }, {
    message: "End date/time cannot be earlier than start date/time.",
    path: ["endDate"],
  });

const steps = [
  { id: 1, title: "Setup", component: EventStep1SetupForm, fields: ['eventName', 'eventType', 'eventStatus', 'eventOwner', 'coreTeamMembers', 'parentExhibitionId', 'linkedExhibitionId', 'privacyLevel'] },
  { id: 2, title: "Date & Location", component: EventStep2DateTimeLocationForm, fields: ['locationType', 'startDate', 'startTime', 'endDate', 'endTime', 'registrationDeadline', 'isRecurring'] },
  { id: 3, title: "Objectives", component: EventStep3ObjectivesForm, fields: ['primaryObjective', 'estimatedAttendance', 'feedbackSurveyUrl'] },
  { id: 4, title: "Budget", component: EventStep4BudgetForm, fields: ['estimatedBudget', 'currency'] },
  { id: 5, title: "Theme & Branding", component: EventStep5ThemeForm, fields: ['themeDescription', 'moodboardItems', 'dressCode'] },
  { id: 6, title: "Requirements", component: EventStep6RequirementsForm, fields: ['cateringRequirements', 'avRequirements'] },
  { id: 7, title: "Vendors", component: EventStep7AssignedVendorsForm, fields: ['assignedVendors'] },
  { id: 8, title: "Staffing Plan", component: EventStep8StaffingPlanForm, fields: ['staffingPlan'] },
  { id: 9, title: "Run of Show", component: EventStep9RunOfShowForm, fields: ['runOfShow'] },
  { id: 10, title: "Attachments", component: EventStep10AttachmentsForm, fields: ['attachments'] },
  { id: 11, title: "Custom Fields", component: EventStep11CustomFieldsForm, fields: ['customFields'] },
];

const initialFormValues: Partial<EventFormData> = {
  eventName: "", eventType: "", eventStatus: "", eventOwner: "", coreTeamMembers: [], parentExhibitionId: "", linkedExhibitionId: "",
  locationType: "", venueName: "", address: "", virtualMeetingLink: "",
  startDate: new Date(),
  endDate: new Date(),
  isRecurring: false,
  startTime: "", endTime: "",
  primaryObjective: "", secondaryObjectives: "", successMetrics: "", targetAudience: "", estimatedAttendance: undefined,
  estimatedBudget: undefined, actualSpending: undefined, currency: "", fundingSource: "", budgetNotes: "",
  themeDescription: "", brandingNotes: "",
  moodboardItems: [],
  assignedVendors: [],
  staffingPlan: [],
  runOfShow: [],
  attachments: [], customFields: {},
  // New fields
  privacyLevel: undefined,
  registrationDeadline: undefined,
  dressCode: '',
  cateringRequirements: '',
  avRequirements: '',
  feedbackSurveyUrl: '',
  recurrencePattern: '',
};

function parseStoredData(storedData: string | null): Partial<EventFormData> {
  if (!storedData) return initialFormValues;
  try {
    const parsed = JSON.parse(storedData);
    if (parsed.startDate) parsed.startDate = new Date(parsed.startDate);
    if (parsed.endDate) parsed.endDate = new Date(parsed.endDate);
    if (parsed.registrationDeadline) parsed.registrationDeadline = new Date(parsed.registrationDeadline);
    const numericFields: (keyof EventFormData)[] = ['estimatedAttendance', 'estimatedBudget', 'actualSpending'];
    numericFields.forEach(field => {
      if (parsed[field] && typeof parsed[field] === 'string' && parsed[field] !== '') {
        parsed[field] = Number(parsed[field]);
      } else if (parsed[field] === '') {
         parsed[field] = ''; 
      }
    });
    // Ensure array fields are initialized
    if (!parsed.coreTeamMembers) parsed.coreTeamMembers = [];
    if (!parsed.linkedExhibitionId) parsed.linkedExhibitionId = ""; 
    if (!parsed.moodboardItems) parsed.moodboardItems = [];
    if (!parsed.assignedVendors) parsed.assignedVendors = [];
    if (!parsed.staffingPlan) parsed.staffingPlan = [];
    if (!parsed.runOfShow) parsed.runOfShow = [];
    return { ...initialFormValues, ...parsed };
  } catch (error) {
    console.error("Failed to parse stored event form data:", error);
    return initialFormValues;
  }
}

export default function AddEventPage() {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = React.useState(0);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  
  const [defaultValues, setDefaultValues] = React.useState<Partial<EventFormData>>(initialFormValues);

  React.useEffect(() => {
    const storedData = localStorage.getItem(LOCAL_STORAGE_KEY);
    setDefaultValues(parseStoredData(storedData));
  }, []);

  const methods = useForm<EventFormData>({
    resolver: zodResolver(eventOnboardingSchemaClient),
    mode: "onTouched", 
    defaultValues: defaultValues,
  });
  
  React.useEffect(() => {
     methods.reset(defaultValues);
  }, [defaultValues, methods]);

  const { handleSubmit, trigger, watch, formState: { errors, touchedFields } } = methods;

  React.useEffect(() => {
    const subscription = watch((value) => {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(value));
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  const handleNext = async () => {
    const currentStepFields = steps[currentStep].fields as (keyof EventFormData)[];
    const isValid = await trigger(currentStepFields);
    
    let hasErrorsInTouchedFields = false;
    for (const field of currentStepFields) {
        if (touchedFields[field] && errors[field]) {
            hasErrorsInTouchedFields = true;
            break;
        }
    }

    if (isValid || !hasErrorsInTouchedFields) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    } else {
        toast({
            title: "Validation Error",
            description: "Please correct the errors on the current step.",
            variant: "destructive",
        });
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };
  
  const triggerStepValidation = async (stepIndex: number): Promise<boolean> => {
    const stepFields = steps[stepIndex].fields as (keyof EventFormData)[];
    return await trigger(stepFields);
  };

  async function onSubmit(data: EventFormData) {
    setIsSubmitting(true);
    const result = await addEventAction(data);
    
    if (result?.errors) {
      Object.entries(result.errors).forEach(([key, value]) => {
          if (value && (value as string[]).length > 0) {
            methods.setError(key as keyof EventFormData, { type: 'server', message: (value as string[]).join(', ') });
          }
      });
      toast({
        title: "Error creating event",
        description: result.message || "Please check the form for errors. You might need to go back to previous steps.",
        variant: "destructive",
      });
      for (let i = 0; i < steps.length; i++) {
        const stepFields = steps[i].fields as (keyof EventFormData)[];
        const hasErrorInStep = stepFields.some(field => result.errors && result.errors[field]);
        if (hasErrorInStep) {
          setCurrentStep(i);
          break;
        }
      }
    } else if (result?.message && !result.errors) {
       toast({
        title: "Error creating event",
        description: result.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Event Created",
        description: `"${data.eventName}" has been successfully added.`,
      });
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    }
    setIsSubmitting(false);
  }

  const CurrentStepComponent = steps[currentStep].component;
  const progressPercentage = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/events">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back to Events</span>
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline">Add New Event</h1>
          <p className="text-muted-foreground">Complete all steps to plan your new event.</p>
        </div>
      </div>

      <Card className="shadow-xl rounded-xl border-2 max-w-6xl mx-auto">
        <CardHeader className="pb-4 bg-gradient-to-r from-primary/5 to-accent/5 rounded-t-xl">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            <CardTitle className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Event Onboarding
            </CardTitle>
            <div className="flex items-center gap-2 bg-background/80 px-3 py-1.5 rounded-full border">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">{`Step ${currentStep + 1} of ${steps.length}: ${steps[currentStep].title}`}</span>
            </div>
          </div>
          <Progress value={progressPercentage} className="mt-3 h-3 bg-muted/50" />
        </CardHeader>

        <CardContent className="p-6">
          <StepIndicator
            steps={steps}
            currentStep={currentStep}
            setCurrentStep={setCurrentStep}
            triggerValidation={triggerStepValidation}
          />

          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className="mt-6">
              <div className="min-h-[60vh] max-h-[70vh] overflow-y-auto px-2 py-4 bg-gradient-to-b from-background/50 to-muted/20 rounded-lg border">
                <CurrentStepComponent />
              </div>

              <div className="mt-6 pt-4 flex justify-between items-center gap-3 border-t bg-gradient-to-r from-background to-muted/30 -mx-6 px-6 py-4 rounded-b-xl">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 0 || isSubmitting}
                  className="px-6 h-10 min-h-[2.5rem] max-h-[2.5rem] flex items-center justify-center"
                >
                  Previous
                </Button>
                {currentStep < steps.length - 1 ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    disabled={isSubmitting}
                    className="px-6 h-10 min-h-[2.5rem] max-h-[2.5rem] flex items-center justify-center bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 h-10 min-h-[2.5rem] max-h-[2.5rem] flex items-center justify-center bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Event...
                      </>
                    ) : (
                      "Create Event"
                    )}
                  </Button>
                )}
              </div>

              {Object.keys(errors).length > 0 && currentStep === steps.length -1 && (
                <div className="mt-4 text-sm text-destructive text-center bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                  ⚠️ There are errors in the form. Please review all steps.
                </div>
              )}
            </form>
          </FormProvider>
        </CardContent>
      </Card>
    </div>
  );
}
