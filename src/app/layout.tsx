import type { Metadata } from 'next';
import './globals.css';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from '@/contexts/auth-context';
import { TenantProvider } from '@/contexts/tenant-context';
import { ConditionalTenantProvider } from '@/components/providers/conditional-tenant-provider';

import SecurityInitializer from '@/components/security/SecurityInitializer';
import { cleanupInvalidThemeData } from '@/lib/theme';

// Fix SSR issues with browser globals
if (typeof globalThis !== 'undefined' && typeof globalThis.self === 'undefined') {
  globalThis.self = globalThis;
}


export const generateViewport = () => ({
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
  themeColor: "#2563eb"
});

export const metadata: Metadata = {
  title: 'EVEXA - Event Excellence Automated',
  description: 'EVEXA: Comprehensive AI-enhanced platform for exhibition and event management.',
  icons: {
    icon: "/favicon.ico",
    apple: "/icons/icon-192x192.png",
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "EVEXA"
  },
  formatDetection: {
    telephone: false
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "apple-mobile-web-app-title": "EVEXA",
    "application-name": "EVEXA",
    "msapplication-TileColor": "#2563eb",
    "msapplication-config": "/browserconfig.xml"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
      </head>
      <body className="font-sans antialiased">
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Clean up invalid theme data immediately on page load
              try {
                const themeKeys = ['theme-mode', 'theme-variant', 'evexa-theme', 'theme', 'currentTheme'];
                themeKeys.forEach(key => {
                  const value = localStorage.getItem(key);
                  if (value && (value.includes(' ') || value === 'EVEXA Dark' || value === 'EVEXA Light')) {
                    localStorage.removeItem(key);
                  }
                });
              } catch (e) { /* ignore */ }
            `,
          }}
        />
        <AuthProvider>
          <ConditionalTenantProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              {children}
              <Toaster />
              <SecurityInitializer />
            </ThemeProvider>
          </ConditionalTenantProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
