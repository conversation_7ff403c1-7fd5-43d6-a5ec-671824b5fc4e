"use client";

import * as React from "react";
import { 
  ThemeContext, 
  ThemeMode, 
  ThemeVariant, 
  themes, 
  applyTheme, 
  getStoredTheme, 
  storeTheme, 
  getSystemMode 
} from "@/lib/theme";

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultMode?: ThemeMode;
  defaultVariant?: ThemeVariant;
  storageKey?: string;
  enableSystem?: boolean;
}

export function ThemeProvider({
  children,
  defaultMode = "system",
  defaultVariant = "gold",
  storageKey = "evexa-theme",
  enableSystem = true,
  ...props
}: ThemeProviderProps) {
  const [mode, setModeState] = React.useState<ThemeMode>(defaultMode);
  const [variant, setVariantState] = React.useState<ThemeVariant>(defaultVariant);
  const [systemMode, setSystemMode] = React.useState<'light' | 'dark'>('light');
  const [mounted, setMounted] = React.useState(false);

  // Get effective mode (resolves 'system' to actual mode)
  const effectiveMode = mode === 'system' ? systemMode : mode;
  const theme = themes[variant];

  // Debug logging
  React.useEffect(() => {
    console.log('Theme Provider Debug:', {
      variant,
      mode,
      effectiveMode,
      theme: theme ? theme.name : 'undefined',
      availableThemes: Object.keys(themes)
    });
  }, [variant, mode, effectiveMode, theme]);

  // Initialize theme from storage
  React.useEffect(() => {
    // Clear any old theme system localStorage values that might cause conflicts
    const oldThemeKeys = ['evexa-theme', 'theme-mode', 'theme-variant'];
    oldThemeKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value && (value.includes(' ') || value === 'EVEXA Dark' || value === 'EVEXA Light')) {
        console.log('Clearing old theme system value:', key, value);
        localStorage.removeItem(key);
      }
    });

    const stored = getStoredTheme();
    setModeState(stored.mode);
    setVariantState(stored.variant);
    setSystemMode(getSystemMode());

    // Clear localStorage if we had to fallback to default
    const storedVariant = localStorage.getItem('theme-variant');
    if (storedVariant && !['gold', 'blue', 'grey'].includes(storedVariant)) {
      console.log('Clearing invalid stored theme variant:', storedVariant);
      localStorage.removeItem('theme-variant');
    }

    setMounted(true);
  }, []);

  // Listen for system theme changes
  React.useEffect(() => {
    if (!enableSystem) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemMode(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [enableSystem]);

  // Apply theme when mode or variant changes
  React.useEffect(() => {
    if (!mounted) return;

    // Use fallback theme if current theme is undefined
    const themeToApply = theme || themes.gold;
    if (!themeToApply) {
      console.error('No theme available, cannot apply theme');
      return;
    }

    applyTheme(themeToApply, effectiveMode);
    
    // Update document class for theme mode and variant
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(effectiveMode);

    // Remove existing theme variant classes
    root.classList.remove('theme-gold', 'theme-blue', 'theme-grey');
    // Add current theme variant class
    root.classList.add(`theme-${variant}`);

    // Update document attributes for theme variant
    root.setAttribute('data-theme', variant);
    
    // Store theme preferences
    storeTheme(mode, variant);
  }, [mode, variant, effectiveMode, theme, mounted]);

  const setMode = React.useCallback((newMode: ThemeMode) => {
    setModeState(newMode);
  }, []);

  const setVariant = React.useCallback((newVariant: ThemeVariant) => {
    setVariantState(newVariant);
  }, []);

  const toggleMode = React.useCallback(() => {
    if (mode === 'system') {
      setMode(systemMode === 'dark' ? 'light' : 'dark');
    } else {
      setMode(mode === 'dark' ? 'light' : 'dark');
    }
  }, [mode, systemMode, setMode]);

  const value = React.useMemo(() => ({
    mode,
    variant,
    theme,
    setMode,
    setVariant,
    toggleMode,
    systemMode,
    effectiveMode,
  }), [mode, variant, theme, setMode, setVariant, toggleMode, systemMode, effectiveMode]);

  // Prevent flash of unstyled content
  if (!mounted) {
    return null;
  }

  return (
    <ThemeContext.Provider value={value} {...props}>
      {children}
    </ThemeContext.Provider>
  );
}

// Theme toggle component
interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'button' | 'switch' | 'dropdown';
}

export function ThemeToggle({ className, size = 'md', variant = 'button' }: ThemeToggleProps) {
  const context = React.useContext(ThemeContext);

  if (!context) {
    console.warn('ThemeToggle must be used within a ThemeProvider');
    return null;
  }

  const { mode, effectiveMode, toggleMode } = context;
  
  if (variant === 'button') {
    return (
      <button
        onClick={toggleMode}
        className={`inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground ${
          size === 'sm' ? 'h-8 w-8' : size === 'lg' ? 'h-12 w-12' : 'h-10 w-10'
        } ${className}`}
        aria-label={`Switch to ${effectiveMode === 'dark' ? 'light' : 'dark'} mode`}
      >
        {effectiveMode === 'dark' ? (
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        ) : (
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        )}
      </button>
    );
  }

  // Add other variants (switch, dropdown) as needed
  return null;
}

// Theme selector component
interface ThemeSelectorProps {
  className?: string;
}

export function ThemeSelector({ className }: ThemeSelectorProps) {
  const context = React.useContext(ThemeContext);

  if (!context) {
    console.warn('ThemeSelector must be used within a ThemeProvider');
    return null;
  }

  const { variant, setVariant } = context;
  
  return (
    <div className={`space-y-2 ${className}`}>
      <label className="text-sm font-medium">Theme</label>
      <div className="grid grid-cols-2 gap-2">
        {Object.entries(themes).map(([key, theme]) => (
          <button
            key={key}
            onClick={() => setVariant(key as ThemeVariant)}
            className={`relative flex items-center space-x-2 rounded-lg border p-3 text-left transition-colors hover:bg-accent ${
              variant === key ? 'border-primary bg-accent' : 'border-border'
            }`}
          >
            <div className="flex-1">
              <div className="font-medium">{theme.name}</div>
              <div className="text-xs text-muted-foreground capitalize">{key}</div>
            </div>
            {variant === key && (
              <div className="h-2 w-2 rounded-full bg-primary" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
}

// Custom theme creator (advanced feature)
export function CustomThemeCreator() {
  // This would be a more complex component for creating custom themes
  // For now, we'll keep it simple
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Custom Theme Creator</h3>
      <p className="text-sm text-muted-foreground">
        Custom theme creation coming soon. For now, choose from the predefined themes above.
      </p>
    </div>
  );
}

// Theme preview component
interface ThemePreviewProps {
  variant: ThemeVariant;
  mode: 'light' | 'dark';
  className?: string;
}

export function ThemePreview({ variant, mode, className }: ThemePreviewProps) {
  const theme = themes[variant];
  const colors = theme.colors[mode];
  
  return (
    <div className={`rounded-lg border p-4 space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <h4 className="font-medium">{theme.name}</h4>
        <span className="text-xs text-muted-foreground capitalize">{mode}</span>
      </div>
      
      <div className="grid grid-cols-6 gap-2">
        <div 
          className="h-8 rounded border"
          style={{ backgroundColor: `hsl(${colors.primary})` }}
          title="Primary"
        />
        <div 
          className="h-8 rounded border"
          style={{ backgroundColor: `hsl(${colors.secondary})` }}
          title="Secondary"
        />
        <div 
          className="h-8 rounded border"
          style={{ backgroundColor: `hsl(${colors.accent})` }}
          title="Accent"
        />
        <div 
          className="h-8 rounded border"
          style={{ backgroundColor: `hsl(${colors.destructive})` }}
          title="Destructive"
        />
        <div 
          className="h-8 rounded border"
          style={{ backgroundColor: `hsl(${colors.success})` }}
          title="Success"
        />
        <div 
          className="h-8 rounded border"
          style={{ backgroundColor: `hsl(${colors.warning})` }}
          title="Warning"
        />
      </div>
    </div>
  );
}
