/**
 * EVEXA Collection Schema Definitions v1.0
 * 
 * Comprehensive schema validation for all standardized collections
 * Prevents future data chaos and ensures data integrity
 */

import { z } from 'zod';
import { StandardMetadata, BaseEntity, CollectionSchema } from './professionalDataManager';

/**
 * Standard Metadata Schema for Zod validation
 */
export const StandardMetadataSchema = z.object({
  tenant_id: z.string().min(1, 'Tenant ID is required'),
  created_at: z.date(),
  updated_at: z.date(),
  created_by: z.string().min(1, 'Created by is required'),
  updated_by: z.string().min(1, 'Updated by is required'),
  version: z.number().int().positive('Version must be positive'),
  deleted_at: z.date().optional(),
  deleted_by: z.string().optional(),
});

/**
 * Base Entity Schema
 */
export const BaseEntitySchema = StandardMetadataSchema.extend({
  id: z.string().min(1, 'ID is required'),
});

/**
 * Core Collection Schemas - Tier 1
 */

// User Management Module
export const UserProfileSchema = BaseEntitySchema.extend({
  email: z.string().email('Invalid email format'),
  name: z.string().min(1, 'Name is required'),
  role: z.enum(['super_admin', 'admin', 'management', 'user']),
  status: z.enum(['active', 'inactive', 'suspended']),
  avatar_url: z.string().url().optional(),
  phone: z.string().optional(),
  department: z.string().optional(),
  job_title: z.string().optional(),
  last_login: z.date().optional(),
  preferences: z.record(z.any()).optional(),
});

export const UserGroupSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Group name is required'),
  description: z.string().optional(),
  permissions: z.array(z.string()),
  member_ids: z.array(z.string()),
  group_type: z.enum(['department', 'project', 'role', 'custom']),
  is_active: z.boolean().default(true),
});

export const UserSettingsSchema = BaseEntitySchema.extend({
  user_id: z.string().min(1, 'User ID is required'),
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().default('en'),
  timezone: z.string().default('UTC'),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    sms: z.boolean().default(false),
  }),
  dashboard_widgets: z.array(z.string()).default([]),
  privacy_settings: z.record(z.boolean()).default({}),
});

// Exhibition Management Module
export const ExhibitionSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Exhibition name is required'),
  description: z.string().optional(),
  venue: z.string().min(1, 'Venue is required'),
  city: z.string().min(1, 'City is required'),
  country: z.string().min(1, 'Country is required'),
  start_date: z.date(),
  end_date: z.date(),
  status: z.enum(['planning', 'confirmed', 'active', 'completed', 'cancelled']),
  budget: z.number().nonnegative().optional(),
  currency: z.string().length(3).optional(), // ISO currency code
  booth_number: z.string().optional(),
  booth_size: z.string().optional(),
  industry: z.string().optional(),
  website_url: z.string().url().optional(),
  contact_person_id: z.string().optional(),
  team_member_ids: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
});

export const ExhibitionEventSchema = BaseEntitySchema.extend({
  exhibition_id: z.string().min(1, 'Exhibition ID is required'),
  title: z.string().min(1, 'Event title is required'),
  description: z.string().optional(),
  event_type: z.enum(['meeting', 'presentation', 'demo', 'networking', 'setup', 'breakdown', 'other']),
  start_time: z.date(),
  end_time: z.date(),
  location: z.string().optional(),
  attendee_ids: z.array(z.string()).default([]),
  organizer_id: z.string().min(1, 'Organizer ID is required'),
  status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  notes: z.string().optional(),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string().url(),
    type: z.string(),
    size: z.number().optional(),
  })).default([]),
});

export const ExhibitionTaskSchema = BaseEntitySchema.extend({
  exhibition_id: z.string().min(1, 'Exhibition ID is required'),
  title: z.string().min(1, 'Task title is required'),
  description: z.string().optional(),
  task_type: z.enum(['setup', 'logistics', 'marketing', 'follow_up', 'admin', 'other']),
  status: z.enum(['todo', 'in_progress', 'review', 'completed', 'cancelled']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assigned_to_id: z.string().optional(),
  due_date: z.date().optional(),
  estimated_hours: z.number().nonnegative().optional(),
  actual_hours: z.number().nonnegative().optional(),
  dependencies: z.array(z.string()).default([]), // Task IDs
  tags: z.array(z.string()).default([]),
  checklist: z.array(z.object({
    item: z.string(),
    completed: z.boolean().default(false),
  })).default([]),
});

// Contact Management Module
export const ContactSchema = BaseEntitySchema.extend({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().optional(),
  company: z.string().optional(),
  job_title: z.string().optional(),
  contact_type: z.enum(['lead', 'customer', 'vendor', 'partner', 'media', 'other']),
  source: z.enum(['exhibition', 'website', 'referral', 'cold_outreach', 'social_media', 'other']).optional(),
  status: z.enum(['new', 'qualified', 'contacted', 'nurturing', 'converted', 'inactive']),
  lead_score: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  social_links: z.object({
    linkedin: z.string().url().optional(),
    twitter: z.string().url().optional(),
    facebook: z.string().url().optional(),
  }).optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postal_code: z.string().optional(),
    country: z.string().optional(),
  }).optional(),
  tags: z.array(z.string()).default([]),
});

// Financial Management Module
export const BudgetSchema = BaseEntitySchema.extend({
  exhibition_id: z.string().min(1, 'Exhibition ID is required'),
  name: z.string().min(1, 'Budget name is required'),
  total_budget: z.number().nonnegative(),
  currency: z.string().length(3), // ISO currency code
  budget_categories: z.array(z.object({
    category: z.string(),
    allocated_amount: z.number().nonnegative(),
    spent_amount: z.number().nonnegative().default(0),
    description: z.string().optional(),
  })),
  approval_status: z.enum(['draft', 'pending', 'approved', 'rejected']),
  approved_by_id: z.string().optional(),
  approved_at: z.date().optional(),
  fiscal_year: z.string().optional(),
  notes: z.string().optional(),
});

export const ExpenseSchema = BaseEntitySchema.extend({
  exhibition_id: z.string().min(1, 'Exhibition ID is required'),
  budget_id: z.string().optional(),
  title: z.string().min(1, 'Expense title is required'),
  description: z.string().optional(),
  amount: z.number().nonnegative(),
  currency: z.string().length(3), // ISO currency code
  category: z.string().min(1, 'Category is required'),
  expense_date: z.date(),
  vendor: z.string().optional(),
  payment_method: z.enum(['cash', 'credit_card', 'bank_transfer', 'check', 'other']).optional(),
  status: z.enum(['pending', 'approved', 'paid', 'rejected']),
  receipt_url: z.string().url().optional(),
  approved_by_id: z.string().optional(),
  approved_at: z.date().optional(),
  tags: z.array(z.string()).default([]),
});

/**
 * Collection Schema Registry
 * Maps collection names to their validation schemas and metadata
 */
export const COLLECTION_SCHEMAS: Record<string, {
  schema: z.ZodSchema;
  metadata: CollectionSchema;
}> = {
  // Core Collections - User Management
  user_profiles: {
    schema: UserProfileSchema,
    metadata: {
      name: 'user_profiles',
      tier: 'core',
      module: 'User Management',
      description: 'Primary user entity with authentication and profile data',
      requiredFields: ['id', 'email', 'name', 'role', 'status'],
      optionalFields: ['avatar_url', 'phone', 'department', 'job_title', 'last_login', 'preferences'],
      indexes: [
        { fields: ['email'], unique: true },
        { fields: ['tenant_id', 'status'] },
        { fields: ['role'] },
      ],
    },
  },
  user_groups: {
    schema: UserGroupSchema,
    metadata: {
      name: 'user_groups',
      tier: 'core',
      module: 'User Management',
      description: 'User grouping and permissions management',
      requiredFields: ['id', 'name', 'permissions', 'member_ids', 'group_type'],
      optionalFields: ['description', 'is_active'],
      relationships: [
        { collection: 'user_profiles', field: 'member_ids', type: 'one-to-many' },
      ],
    },
  },
  user_settings: {
    schema: UserSettingsSchema,
    metadata: {
      name: 'user_settings',
      tier: 'core',
      module: 'User Management',
      description: 'User preferences and application settings',
      requiredFields: ['id', 'user_id'],
      optionalFields: ['theme', 'language', 'timezone', 'notifications', 'dashboard_widgets', 'privacy_settings'],
      relationships: [
        { collection: 'user_profiles', field: 'user_id', type: 'one-to-one' },
      ],
      indexes: [
        { fields: ['user_id'], unique: true },
      ],
    },
  },
  // Core Collections - Exhibition Management
  exhibitions: {
    schema: ExhibitionSchema,
    metadata: {
      name: 'exhibitions',
      tier: 'core',
      module: 'Exhibition Management',
      description: 'Primary exhibition entity with venue and scheduling data',
      requiredFields: ['id', 'name', 'venue', 'city', 'country', 'start_date', 'end_date', 'status'],
      optionalFields: ['description', 'budget', 'currency', 'booth_number', 'booth_size', 'industry', 'website_url', 'contact_person_id', 'team_member_ids', 'tags'],
      relationships: [
        { collection: 'user_profiles', field: 'contact_person_id', type: 'one-to-one' },
        { collection: 'user_profiles', field: 'team_member_ids', type: 'one-to-many' },
      ],
      indexes: [
        { fields: ['tenant_id', 'status'] },
        { fields: ['start_date', 'end_date'] },
        { fields: ['city', 'country'] },
      ],
    },
  },
  exhibition_events: {
    schema: ExhibitionEventSchema,
    metadata: {
      name: 'exhibition_events',
      tier: 'core',
      module: 'Exhibition Management',
      description: 'Events and activities within exhibitions',
      requiredFields: ['id', 'exhibition_id', 'title', 'event_type', 'start_time', 'end_time', 'organizer_id', 'status'],
      optionalFields: ['description', 'location', 'attendee_ids', 'priority', 'notes', 'attachments'],
      relationships: [
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' },
        { collection: 'user_profiles', field: 'organizer_id', type: 'one-to-one' },
        { collection: 'user_profiles', field: 'attendee_ids', type: 'one-to-many' },
      ],
      indexes: [
        { fields: ['exhibition_id'] },
        { fields: ['start_time', 'end_time'] },
        { fields: ['status'] },
      ],
    },
  },
  exhibition_tasks: {
    schema: ExhibitionTaskSchema,
    metadata: {
      name: 'exhibition_tasks',
      tier: 'core',
      module: 'Exhibition Management',
      description: 'Tasks and activities for exhibition management',
      requiredFields: ['id', 'exhibition_id', 'title', 'task_type', 'status'],
      optionalFields: ['description', 'priority', 'assigned_to_id', 'due_date', 'estimated_hours', 'actual_hours', 'dependencies', 'tags', 'checklist'],
      relationships: [
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' },
        { collection: 'user_profiles', field: 'assigned_to_id', type: 'one-to-one' },
        { collection: 'exhibition_tasks', field: 'dependencies', type: 'many-to-many' },
      ],
      indexes: [
        { fields: ['exhibition_id'] },
        { fields: ['assigned_to_id'] },
        { fields: ['status'] },
        { fields: ['due_date'] },
      ],
    },
  },
  // Core Collections - Contact Management
  contacts: {
    schema: ContactSchema,
    metadata: {
      name: 'contacts',
      tier: 'core',
      module: 'Contact Management',
      description: 'Contact and lead management system',
      requiredFields: ['id', 'first_name', 'last_name', 'contact_type', 'status'],
      optionalFields: ['email', 'phone', 'company', 'job_title', 'source', 'lead_score', 'notes', 'social_links', 'address', 'tags'],
      indexes: [
        { fields: ['email'] },
        { fields: ['tenant_id', 'contact_type'] },
        { fields: ['status'] },
        { fields: ['company'] },
      ],
    },
  },
  // Core Collections - Financial Management
  budgets: {
    schema: BudgetSchema,
    metadata: {
      name: 'budgets',
      tier: 'core',
      module: 'Financial Management',
      description: 'Budget planning and allocation for exhibitions',
      requiredFields: ['id', 'exhibition_id', 'name', 'total_budget', 'currency', 'budget_categories', 'approval_status'],
      optionalFields: ['approved_by_id', 'approved_at', 'fiscal_year', 'notes'],
      relationships: [
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' },
        { collection: 'user_profiles', field: 'approved_by_id', type: 'one-to-one' },
      ],
      indexes: [
        { fields: ['exhibition_id'] },
        { fields: ['approval_status'] },
        { fields: ['fiscal_year'] },
      ],
    },
  },
  expenses: {
    schema: ExpenseSchema,
    metadata: {
      name: 'expenses',
      tier: 'core',
      module: 'Financial Management',
      description: 'Expense tracking and management',
      requiredFields: ['id', 'exhibition_id', 'title', 'amount', 'currency', 'category', 'expense_date', 'status'],
      optionalFields: ['budget_id', 'description', 'vendor', 'payment_method', 'receipt_url', 'approved_by_id', 'approved_at', 'tags'],
      relationships: [
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' },
        { collection: 'budgets', field: 'budget_id', type: 'one-to-one' },
        { collection: 'user_profiles', field: 'approved_by_id', type: 'one-to-one' },
      ],
      indexes: [
        { fields: ['exhibition_id'] },
        { fields: ['budget_id'] },
        { fields: ['status'] },
        { fields: ['expense_date'] },
      ],
    },
  },
};

/**
 * Validation helper functions
 */
export function validateCollectionData(collectionName: string, data: any): {
  isValid: boolean;
  errors: string[];
  data?: any;
} {
  const collectionSchema = COLLECTION_SCHEMAS[collectionName];
  
  if (!collectionSchema) {
    return {
      isValid: false,
      errors: [`Unknown collection: ${collectionName}`],
    };
  }

  try {
    const validatedData = collectionSchema.schema.parse(data);
    return {
      isValid: true,
      errors: [],
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
      };
    }
    
    return {
      isValid: false,
      errors: [`Validation error: ${error}`],
    };
  }
}

export function getCollectionMetadata(collectionName: string): CollectionSchema | null {
  const collectionSchema = COLLECTION_SCHEMAS[collectionName];
  return collectionSchema ? collectionSchema.metadata : null;
}

export function getAllCollectionNames(): string[] {
  return Object.keys(COLLECTION_SCHEMAS);
}

export function getCollectionsByTier(tier: 'core' | 'business' | 'extended'): string[] {
  return Object.entries(COLLECTION_SCHEMAS)
    .filter(([_, config]) => config.metadata.tier === tier)
    .map(([name]) => name);
}

export function getCollectionsByModule(module: string): string[] {
  return Object.entries(COLLECTION_SCHEMAS)
    .filter(([_, config]) => config.metadata.module === module)
    .map(([name]) => name);
}

/**
 * Business Collection Schemas - Tier 2
 */

// Communication Module
export const EmailCampaignSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Campaign name is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  campaign_type: z.enum(['newsletter', 'promotional', 'follow_up', 'announcement', 'invitation']),
  status: z.enum(['draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled']),
  target_audience: z.enum(['all_contacts', 'leads', 'customers', 'custom_segment']),
  recipient_ids: z.array(z.string()).default([]),
  scheduled_at: z.date().optional(),
  sent_at: z.date().optional(),
  open_rate: z.number().min(0).max(1).optional(),
  click_rate: z.number().min(0).max(1).optional(),
  bounce_rate: z.number().min(0).max(1).optional(),
  unsubscribe_rate: z.number().min(0).max(1).optional(),
  template_id: z.string().optional(),
  tags: z.array(z.string()).default([]),
});

export const EmailTemplateSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Template name is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  template_type: z.enum(['welcome', 'follow_up', 'newsletter', 'promotional', 'thank_you', 'custom']),
  is_active: z.boolean().default(true),
  variables: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    default_value: z.string().optional(),
  })).default([]),
  preview_text: z.string().optional(),
  category: z.string().optional(),
});

// Analytics Module
export const AnalyticsEventSchema = BaseEntitySchema.extend({
  event_name: z.string().min(1, 'Event name is required'),
  event_type: z.enum(['page_view', 'click', 'form_submit', 'download', 'custom']),
  user_id: z.string().optional(),
  session_id: z.string().optional(),
  page_url: z.string().url().optional(),
  referrer: z.string().url().optional(),
  user_agent: z.string().optional(),
  ip_address: z.string().optional(),
  properties: z.record(z.any()).default({}),
  timestamp: z.date(),
  exhibition_id: z.string().optional(),
  contact_id: z.string().optional(),
});

export const ReportSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Report name is required'),
  description: z.string().optional(),
  report_type: z.enum(['exhibition_performance', 'lead_analysis', 'financial_summary', 'task_progress', 'custom']),
  data_source: z.array(z.string()),
  filters: z.record(z.any()).default({}),
  date_range: z.object({
    start_date: z.date(),
    end_date: z.date(),
  }),
  schedule: z.object({
    frequency: z.enum(['once', 'daily', 'weekly', 'monthly', 'quarterly']),
    next_run: z.date().optional(),
  }).optional(),
  recipients: z.array(z.string()).default([]),
  format: z.enum(['pdf', 'excel', 'csv', 'json']).default('pdf'),
  is_active: z.boolean().default(true),
});

// Logistics Module
export const ShipmentSchema = BaseEntitySchema.extend({
  exhibition_id: z.string().min(1, 'Exhibition ID is required'),
  tracking_number: z.string().optional(),
  carrier: z.string().optional(),
  shipment_type: z.enum(['inbound', 'outbound', 'return']),
  status: z.enum(['pending', 'picked_up', 'in_transit', 'delivered', 'delayed', 'lost']),
  origin: z.object({
    address: z.string(),
    city: z.string(),
    state: z.string().optional(),
    postal_code: z.string(),
    country: z.string(),
  }),
  destination: z.object({
    address: z.string(),
    city: z.string(),
    state: z.string().optional(),
    postal_code: z.string(),
    country: z.string(),
  }),
  items: z.array(z.object({
    description: z.string(),
    quantity: z.number().int().positive(),
    weight: z.number().positive().optional(),
    dimensions: z.object({
      length: z.number().positive(),
      width: z.number().positive(),
      height: z.number().positive(),
      unit: z.enum(['cm', 'in']),
    }).optional(),
    value: z.number().nonnegative().optional(),
  })),
  shipped_date: z.date().optional(),
  expected_delivery: z.date().optional(),
  actual_delivery: z.date().optional(),
  cost: z.number().nonnegative().optional(),
  currency: z.string().length(3).optional(),
  notes: z.string().optional(),
});

export const InventoryItemSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  sku: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  quantity_available: z.number().int().nonnegative(),
  quantity_reserved: z.number().int().nonnegative().default(0),
  unit_cost: z.number().nonnegative().optional(),
  currency: z.string().length(3).optional(),
  supplier: z.string().optional(),
  location: z.string().optional(),
  condition: z.enum(['new', 'used', 'refurbished', 'damaged']).default('new'),
  last_audit_date: z.date().optional(),
  reorder_level: z.number().int().nonnegative().optional(),
  tags: z.array(z.string()).default([]),
  images: z.array(z.string().url()).default([]),
});

// Performance Module
export const KpiSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'KPI name is required'),
  description: z.string().optional(),
  category: z.enum(['financial', 'operational', 'marketing', 'customer', 'quality']),
  metric_type: z.enum(['count', 'percentage', 'currency', 'time', 'ratio']),
  target_value: z.number().optional(),
  current_value: z.number(),
  unit: z.string().optional(),
  calculation_method: z.string().optional(),
  data_source: z.string().optional(),
  frequency: z.enum(['real_time', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly']),
  exhibition_id: z.string().optional(),
  department: z.string().optional(),
  owner_id: z.string().optional(),
  last_updated: z.date(),
  trend: z.enum(['up', 'down', 'stable']).optional(),
  status: z.enum(['on_track', 'at_risk', 'off_track']).optional(),
});

// Add these to the COLLECTION_SCHEMAS registry
const BUSINESS_COLLECTION_SCHEMAS = {
  // Communication Module
  email_campaigns: {
    schema: EmailCampaignSchema,
    metadata: {
      name: 'email_campaigns',
      tier: 'business' as const,
      module: 'Communication',
      description: 'Email marketing campaigns and automation',
      requiredFields: ['id', 'name', 'subject', 'content', 'campaign_type', 'status', 'target_audience'],
      optionalFields: ['recipient_ids', 'scheduled_at', 'sent_at', 'open_rate', 'click_rate', 'bounce_rate', 'unsubscribe_rate', 'template_id', 'tags'],
      relationships: [
        { collection: 'contacts', field: 'recipient_ids', type: 'one-to-many' as const },
        { collection: 'email_templates', field: 'template_id', type: 'one-to-one' as const },
      ],
      indexes: [
        { fields: ['status'] },
        { fields: ['campaign_type'] },
        { fields: ['scheduled_at'] },
      ],
    },
  },
  email_templates: {
    schema: EmailTemplateSchema,
    metadata: {
      name: 'email_templates',
      tier: 'business' as const,
      module: 'Communication',
      description: 'Reusable email templates for campaigns',
      requiredFields: ['id', 'name', 'subject', 'content', 'template_type'],
      optionalFields: ['is_active', 'variables', 'preview_text', 'category'],
      indexes: [
        { fields: ['template_type'] },
        { fields: ['is_active'] },
        { fields: ['category'] },
      ],
    },
  },
  // Analytics Module
  analytics_events: {
    schema: AnalyticsEventSchema,
    metadata: {
      name: 'analytics_events',
      tier: 'business' as const,
      module: 'Analytics',
      description: 'User behavior and system events tracking',
      requiredFields: ['id', 'event_name', 'event_type', 'timestamp'],
      optionalFields: ['user_id', 'session_id', 'page_url', 'referrer', 'user_agent', 'ip_address', 'properties', 'exhibition_id', 'contact_id'],
      relationships: [
        { collection: 'user_profiles', field: 'user_id', type: 'one-to-one' as const },
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' as const },
        { collection: 'contacts', field: 'contact_id', type: 'one-to-one' as const },
      ],
      indexes: [
        { fields: ['event_type'] },
        { fields: ['timestamp'] },
        { fields: ['user_id'] },
        { fields: ['exhibition_id'] },
      ],
    },
  },
  reports: {
    schema: ReportSchema,
    metadata: {
      name: 'reports',
      tier: 'business' as const,
      module: 'Analytics',
      description: 'Automated reporting and data analysis',
      requiredFields: ['id', 'name', 'report_type', 'data_source', 'date_range'],
      optionalFields: ['description', 'filters', 'schedule', 'recipients', 'format', 'is_active'],
      relationships: [
        { collection: 'user_profiles', field: 'recipients', type: 'one-to-many' as const },
      ],
      indexes: [
        { fields: ['report_type'] },
        { fields: ['is_active'] },
      ],
    },
  },
  // Logistics Module
  shipments: {
    schema: ShipmentSchema,
    metadata: {
      name: 'shipments',
      tier: 'business' as const,
      module: 'Logistics',
      description: 'Shipment tracking and logistics management',
      requiredFields: ['id', 'exhibition_id', 'shipment_type', 'status', 'origin', 'destination', 'items'],
      optionalFields: ['tracking_number', 'carrier', 'shipped_date', 'expected_delivery', 'actual_delivery', 'cost', 'currency', 'notes'],
      relationships: [
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' as const },
      ],
      indexes: [
        { fields: ['exhibition_id'] },
        { fields: ['status'] },
        { fields: ['tracking_number'] },
        { fields: ['expected_delivery'] },
      ],
    },
  },
  inventory_items: {
    schema: InventoryItemSchema,
    metadata: {
      name: 'inventory_items',
      tier: 'business' as const,
      module: 'Logistics',
      description: 'Inventory and asset management',
      requiredFields: ['id', 'name', 'category', 'quantity_available'],
      optionalFields: ['description', 'sku', 'quantity_reserved', 'unit_cost', 'currency', 'supplier', 'location', 'condition', 'last_audit_date', 'reorder_level', 'tags', 'images'],
      indexes: [
        { fields: ['category'] },
        { fields: ['sku'], unique: true },
        { fields: ['quantity_available'] },
        { fields: ['location'] },
      ],
    },
  },
  // Performance Module
  kpis: {
    schema: KpiSchema,
    metadata: {
      name: 'kpis',
      tier: 'business' as const,
      module: 'Performance',
      description: 'Key Performance Indicators tracking',
      requiredFields: ['id', 'name', 'category', 'metric_type', 'current_value', 'frequency', 'last_updated'],
      optionalFields: ['description', 'target_value', 'unit', 'calculation_method', 'data_source', 'exhibition_id', 'department', 'owner_id', 'trend', 'status'],
      relationships: [
        { collection: 'exhibitions', field: 'exhibition_id', type: 'one-to-one' as const },
        { collection: 'user_profiles', field: 'owner_id', type: 'one-to-one' as const },
      ],
      indexes: [
        { fields: ['category'] },
        { fields: ['exhibition_id'] },
        { fields: ['department'] },
        { fields: ['last_updated'] },
      ],
    },
  },
};

// Merge business schemas into main registry
Object.assign(COLLECTION_SCHEMAS, BUSINESS_COLLECTION_SCHEMAS);
