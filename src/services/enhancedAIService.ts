/**
 * Enhanced AI Service for EVEXA
 * Integrates AI functionality with cost optimization, caching, and usage monitoring
 */

import { toast } from 'sonner';
import { aiCostOptimizationService } from './aiCostOptimizationService';
// Temporarily disabled to fix circular dependencies
// import { aiCacheStrategyService } from './aiCacheStrategyService';
import { getMockAIResponse } from '@/lib/ai-wrapper';
import { productionAIService } from './productionAIService';

export interface EnhancedAIRequest {
  service: string;
  prompt: string;
  context?: any;
  userId?: string;
  tenantId?: string;
  priority?: 'low' | 'normal' | 'high';
  maxCost?: number;
  cacheTTL?: number;
  requireRealAI?: boolean; // Force real AI even in development
}

export interface EnhancedAIResponse {
  response: any;
  metadata: {
    cached: boolean;
    cost: number;
    tokensUsed: number;
    responseTime: number;
    provider: string;
    model: string;
    requestId: string;
  };
}

export interface AIServiceTier {
  name: string;
  maxDailyCost: number;
  maxMonthlyCost: number;
  features: string[];
  requestsPerMinute: number;
  requestsPerDay: number;
  cacheTTL: number;
}

class EnhancedAIService {
  private initialized = false;
  private serviceTiers: Record<string, AIServiceTier> = {
    free: {
      name: 'Free',
      maxDailyCost: 1.00,
      maxMonthlyCost: 10.00,
      features: ['virtualAssistant', 'basicInsights'],
      requestsPerMinute: 5,
      requestsPerDay: 50,
      cacheTTL: 120 // 2 hours
    },
    basic: {
      name: 'Basic',
      maxDailyCost: 5.00,
      maxMonthlyCost: 50.00,
      features: ['virtualAssistant', 'basicInsights', 'budgetPrediction', 'leadScoring'],
      requestsPerMinute: 15,
      requestsPerDay: 200,
      cacheTTL: 60 // 1 hour
    },
    premium: {
      name: 'Premium',
      maxDailyCost: 15.00,
      maxMonthlyCost: 200.00,
      features: ['virtualAssistant', 'basicInsights', 'budgetPrediction', 'leadScoring', 'contentGeneration', 'workflowAutomation'],
      requestsPerMinute: 30,
      requestsPerDay: 500,
      cacheTTL: 30 // 30 minutes
    },
    enterprise: {
      name: 'Enterprise',
      maxDailyCost: 50.00,
      maxMonthlyCost: 1000.00,
      features: ['virtualAssistant', 'basicInsights', 'budgetPrediction', 'leadScoring', 'contentGeneration', 'workflowAutomation', 'anomalyDetection', 'performanceInsights'],
      requestsPerMinute: 60,
      requestsPerDay: 2000,
      cacheTTL: 15 // 15 minutes
    }
  };

  constructor() {
    this.initialize();
  }

  /**
   * Initialize the enhanced AI service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await aiCostOptimizationService.initializeService();
      await aiInfrastructureService.initialize();
      this.initialized = true;
      console.log('✅ Enhanced AI Service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced AI Service:', error);
    }
  }

  /**
   * Execute AI request with cost optimization and caching
   */
  async executeRequest(request: EnhancedAIRequest): Promise<EnhancedAIResponse> {
    await this.initialize();

    const startTime = Date.now();
    const requestId = `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Check if request is allowed based on cost and rate limits
      const estimatedCost = this.estimateRequestCost(request.prompt);
      const canMakeRequest = await aiCostOptimizationService.canMakeRequest(
        request.service,
        request.maxCost || estimatedCost
      );

      if (!canMakeRequest.allowed) {
        toast.error(`AI request blocked: ${canMakeRequest.reason}`);
        if (canMakeRequest.suggestion) {
          toast.info(canMakeRequest.suggestion);
        }
        
        // Return cached response if available, otherwise mock
        const cachedResponse = aiCostOptimizationService.getCachedResponse(
          request.service,
          request.prompt,
          request.context
        );

        if (cachedResponse) {
          return {
            response: cachedResponse,
            metadata: {
              cached: true,
              cost: 0,
              tokensUsed: 0,
              responseTime: Date.now() - startTime,
              provider: 'cache',
              model: 'cached',
              requestId
            }
          };
        }

        // Return mock response as fallback
        const mockResponse = getMockAIResponse(request.service, {
          query: request.prompt,
          context: request.context
        });

        return {
          response: mockResponse,
          metadata: {
            cached: false,
            cost: 0,
            tokensUsed: 0,
            responseTime: Date.now() - startTime,
            provider: 'mock',
            model: 'mock',
            requestId
          }
        };
      }

      // Check cache first
      const cachedResponse = aiCostOptimizationService.getCachedResponse(
        request.service,
        request.prompt,
        request.context
      );

      if (cachedResponse) {
        return {
          response: cachedResponse,
          metadata: {
            cached: true,
            cost: 0,
            tokensUsed: 0,
            responseTime: Date.now() - startTime,
            provider: 'cache',
            model: 'cached',
            requestId
          }
        };
      }

      // Execute AI request
      let response: any;
      let tokensUsed = 0;
      let actualCost = 0;
      let provider = 'mock';
      let model = 'mock';

      // Use production AI service (reliable and fast)
      try {
        const aiResponse = await productionAIService.executeRequest({
          service: request.service,
          prompt: request.prompt,
          context: request.context,
          maxTokens: request.maxCost ? Math.min(1000, request.maxCost * 10000) : 500
        });

        response = aiResponse.response;
        tokensUsed = aiResponse.tokensUsed;
        actualCost = aiResponse.cost;
        provider = aiResponse.provider;
        model = aiResponse.model;

        console.log(`✅ Production AI response: ${provider} - ${model} - $${actualCost.toFixed(6)}`);

      } catch (error) {
        console.error('Production AI request failed, using mock fallback:', error);

        // Fallback to mock response
        response = getMockAIResponse(request.service, {
          query: request.prompt,
          context: request.context
        });
        tokensUsed = this.estimateTokens(request.prompt);
        actualCost = 0; // No cost for mock fallback
        provider = 'mock_fallback';
        model = 'mock';

        toast.warning('AI request failed, using fallback response');
      }

      const responseTime = Date.now() - startTime;

      // Cache the response
      aiCostOptimizationService.cacheResponse(
        request.service,
        request.prompt,
        response,
        request.context,
        request.cacheTTL
      );

      // Record usage for cost tracking
      await aiCostOptimizationService.recordUsage({
        service: request.service,
        prompt: request.prompt,
        promptTokens: this.estimateTokens(request.prompt),
        completionTokens: this.estimateTokens(JSON.stringify(response)),
        totalTokens: tokensUsed,
        cost: actualCost,
        cacheHit: false,
        responseTime,
        userId: request.userId,
        tenantId: request.tenantId,
        model,
        provider
      });

      return {
        response,
        metadata: {
          cached: false,
          cost: actualCost,
          tokensUsed,
          responseTime,
          provider,
          model,
          requestId
        }
      };

    } catch (error) {
      console.error(`Enhanced AI request failed for ${request.service}:`, error);
      
      // Return mock response as fallback
      const mockResponse = getMockAIResponse(request.service, {
        query: request.prompt,
        context: request.context
      });

      return {
        response: mockResponse,
        metadata: {
          cached: false,
          cost: 0,
          tokensUsed: 0,
          responseTime: Date.now() - startTime,
          provider: 'mock_fallback',
          model: 'mock',
          requestId
        }
      };
    }
  }



  /**
   * Estimate request cost based on prompt length
   */
  private estimateRequestCost(prompt: string): number {
    const estimatedTokens = this.estimateTokens(prompt);
    return this.calculateCost(estimatedTokens);
  }

  /**
   * Estimate tokens from text
   */
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  /**
   * Calculate cost based on tokens
   */
  private calculateCost(tokens: number): number {
    // Gemini 2.0 Flash pricing (approximate)
    const costPerToken = 0.000001; // $0.000001 per token
    return tokens * costPerToken;
  }

  /**
   * Get service tier configuration
   */
  getServiceTier(tierName: string): AIServiceTier | null {
    return this.serviceTiers[tierName] || null;
  }

  /**
   * Update service tier configuration
   */
  updateServiceTier(tierName: string, config: Partial<AIServiceTier>): void {
    if (this.serviceTiers[tierName]) {
      this.serviceTiers[tierName] = { ...this.serviceTiers[tierName], ...config };
    }
  }

  /**
   * Get cost analytics
   */
  async getCostAnalytics() {
    return await aiCostOptimizationService.getCostAnalytics();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return aiCostOptimizationService.getCacheStats();
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    aiCostOptimizationService.clearCache();
  }

  /**
   * Update cost configuration
   */
  async updateCostConfiguration(config: any): Promise<void> {
    await aiCostOptimizationService.updateConfiguration(config);
  }

  /**
   * Check if feature is available for user tier
   */
  isFeatureAvailable(feature: string, userTier: string = 'free'): boolean {
    const tier = this.serviceTiers[userTier];
    return tier ? tier.features.includes(feature) : false;
  }

  /**
   * Get available features for tier
   */
  getAvailableFeatures(userTier: string = 'free'): string[] {
    const tier = this.serviceTiers[userTier];
    return tier ? tier.features : [];
  }
}

// Export singleton instance
export const enhancedAIService = new EnhancedAIService();
