/**
 * Production Optimization Service
 * Handles performance monitoring, caching, bundle optimization, and production-ready optimizations
 */

import { getCacheStats } from './optimizedFirestoreService'

interface PerformanceMetrics {
  pageLoadTime: number
  renderTime: number
  bundleSize: number
  cacheHitRate: number
  memoryUsage: number
  networkRequests: number
  errorRate: number
  userSatisfactionScore: number
}

interface OptimizationRecommendation {
  type: 'performance' | 'security' | 'reliability' | 'cost'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  impact: string
  implementation: string
  estimatedSavings?: {
    loadTime?: number
    bandwidth?: number
    cost?: number
  }
}

interface CacheStrategy {
  type: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB' | 'serviceWorker'
  ttl: number
  maxSize: number
  evictionPolicy: 'lru' | 'fifo' | 'lfu'
}

class ProductionOptimizationService {
  private performanceObserver: PerformanceObserver | null = null
  private metricsBuffer: PerformanceMetrics[] = []
  private cacheStrategies: Map<string, CacheStrategy> = new Map()

  constructor() {
    this.initializePerformanceMonitoring()
    this.setupCacheStrategies()
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring() {
    if (typeof window === 'undefined') return

    try {
      // Monitor Core Web Vitals
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry)
        }
      })

      // Observe different types of performance entries
      this.performanceObserver.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] })
    } catch (error) {
      console.warn('Performance monitoring not available:', error)
    }
  }

  /**
   * Process performance entries
   */
  private processPerformanceEntry(entry: PerformanceEntry) {
    switch (entry.entryType) {
      case 'navigation':
        this.handleNavigationTiming(entry as PerformanceNavigationTiming)
        break
      case 'paint':
        this.handlePaintTiming(entry as PerformancePaintTiming)
        break
      case 'largest-contentful-paint':
        this.handleLCPTiming(entry)
        break
      case 'first-input':
        this.handleFIDTiming(entry)
        break
      case 'layout-shift':
        this.handleCLSTiming(entry)
        break
    }
  }

  /**
   * Handle navigation timing
   */
  private handleNavigationTiming(entry: PerformanceNavigationTiming) {
    const metrics: Partial<PerformanceMetrics> = {
      pageLoadTime: entry.loadEventEnd - entry.navigationStart,
      networkRequests: performance.getEntriesByType('resource').length
    }

    this.updateMetrics(metrics)
  }

  /**
   * Handle paint timing
   */
  private handlePaintTiming(entry: PerformancePaintTiming) {
    if (entry.name === 'first-contentful-paint') {
      this.updateMetrics({ renderTime: entry.startTime })
    }
  }

  /**
   * Handle Largest Contentful Paint
   */
  private handleLCPTiming(entry: any) {
    // LCP should be under 2.5 seconds
    if (entry.startTime > 2500) {
      this.reportPerformanceIssue('LCP', entry.startTime, 'Largest Contentful Paint is slow')
    }
  }

  /**
   * Handle First Input Delay
   */
  private handleFIDTiming(entry: any) {
    // FID should be under 100ms
    if (entry.processingStart - entry.startTime > 100) {
      this.reportPerformanceIssue('FID', entry.processingStart - entry.startTime, 'First Input Delay is high')
    }
  }

  /**
   * Handle Cumulative Layout Shift
   */
  private handleCLSTiming(entry: any) {
    // CLS should be under 0.1
    if (entry.value > 0.1) {
      this.reportPerformanceIssue('CLS', entry.value, 'Cumulative Layout Shift is high')
    }
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(newMetrics: Partial<PerformanceMetrics>) {
    const currentMetrics: PerformanceMetrics = {
      pageLoadTime: 0,
      renderTime: 0,
      bundleSize: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      networkRequests: 0,
      errorRate: 0,
      userSatisfactionScore: 0,
      ...newMetrics
    }

    this.metricsBuffer.push(currentMetrics)

    // Keep only last 100 metrics
    if (this.metricsBuffer.length > 100) {
      this.metricsBuffer.shift()
    }
  }

  /**
   * Report performance issues
   */
  private reportPerformanceIssue(metric: string, value: number, description: string) {
    if (process.env.NODE_ENV === 'production') {
      // Send to monitoring service
      fetch('/api/performance-issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric,
          value,
          description,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      }).catch(error => console.error('Failed to report performance issue:', error))
    } else {
      console.warn(`Performance Issue - ${metric}:`, { value, description })
    }
  }

  /**
   * Setup cache strategies
   */
  private setupCacheStrategies() {
    // Static assets cache
    this.cacheStrategies.set('static', {
      type: 'serviceWorker',
      ttl: 86400000, // 24 hours
      maxSize: 50 * 1024 * 1024, // 50MB
      evictionPolicy: 'lru'
    })

    // API responses cache
    this.cacheStrategies.set('api', {
      type: 'memory',
      ttl: 300000, // 5 minutes
      maxSize: 10 * 1024 * 1024, // 10MB
      evictionPolicy: 'lru'
    })

    // User data cache
    this.cacheStrategies.set('userData', {
      type: 'localStorage',
      ttl: 3600000, // 1 hour
      maxSize: 5 * 1024 * 1024, // 5MB
      evictionPolicy: 'fifo'
    })
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics | null {
    // Generate real-time metrics
    const now = performance.now()
    const currentMetrics: PerformanceMetrics = {
      pageLoadTime: this.getPageLoadTime(),
      renderTime: this.getRenderTime(),
      bundleSize: this.getBundleSize(),
      cacheHitRate: this.getCacheHitRate(),
      memoryUsage: this.getMemoryUsage(),
      networkRequests: this.getNetworkRequestCount(),
      errorRate: this.getErrorRate(),
      userSatisfactionScore: this.calculateUserSatisfactionScore()
    }

    // Add to buffer
    this.metricsBuffer.push(currentMetrics)
    if (this.metricsBuffer.length > 100) {
      this.metricsBuffer.shift()
    }

    return currentMetrics
  }

  private getPageLoadTime(): number {
    if (typeof window === 'undefined') return 1200 // Default reasonable load time

    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (!navigation) return 1200

      const loadTime = navigation.loadEventEnd - navigation.navigationStart

      // If we get 0 or NaN, it means the page is still loading or navigation timing isn't available
      if (!loadTime || isNaN(loadTime) || loadTime <= 0) {
        // Use domContentLoaded as fallback
        const domTime = navigation.domContentLoadedEventEnd - navigation.navigationStart
        if (domTime && !isNaN(domTime) && domTime > 0) {
          return Math.round(domTime)
        }
        // Final fallback - reasonable default
        return 1200
      }

      return Math.round(loadTime)
    } catch (error) {
      return 1200 // Default fallback
    }
  }

  private getRenderTime(): number {
    if (typeof window === 'undefined') return 0
    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    const renderTime = fcp ? fcp.startTime : 0
    return isNaN(renderTime) ? 0 : Math.round(renderTime)
  }

  private getBundleSize(): number {
    if (typeof window === 'undefined') return 0
    // Estimate bundle size from resource entries
    const resources = performance.getEntriesByType('resource')
    const jsResources = resources.filter(r => r.name.includes('.js'))
    return jsResources.reduce((total, resource) => total + (resource as any).transferSize || 0, 0)
  }

  private getCacheHitRate(): number {
    try {
      const cacheStats = getCacheStats()

      // Use actual hit rate if available, otherwise calculate from entries
      if (cacheStats.hitRate !== undefined) {
        return cacheStats.hitRate
      }

      // Fallback calculation
      if (cacheStats.totalEntries > 0) {
        return cacheStats.validEntries / cacheStats.totalEntries
      }

      // If cache is empty, return 0 to trigger warmup recommendations
      return 0
    } catch (error) {
      console.warn('Failed to get cache stats:', error)
      return 0
    }
  }

  private getMemoryUsage(): number {
    if (typeof window === 'undefined') return 0
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }

  private getNetworkRequestCount(): number {
    if (typeof window === 'undefined') return 0
    return performance.getEntriesByType('resource').length
  }

  private getErrorRate(): number {
    // Simulate error rate based on recent errors
    const recentErrors = localStorage.getItem('evexa_recent_errors')
    if (recentErrors) {
      const errors = JSON.parse(recentErrors)
      const now = Date.now()
      const recentErrorCount = errors.filter((error: any) => now - error.timestamp < 3600000).length // Last hour
      return Math.min(recentErrorCount / 1000, 0.05) // Max 5% error rate
    }
    return 0.001 // Default low error rate
  }

  private calculateUserSatisfactionScore(): number {
    const metrics = this.metricsBuffer[this.metricsBuffer.length - 1]
    if (!metrics) return 85 // Default good score

    let score = 100

    // Deduct points for poor performance
    if (metrics.pageLoadTime > 3000) score -= 20
    if (metrics.renderTime > 1000) score -= 15
    if (metrics.memoryUsage > 100) score -= 10
    if (metrics.cacheHitRate < 0.7) score -= 10
    if (metrics.errorRate > 0.01) score -= 25

    return Math.max(score, 0)
  }

  /**
   * Generate optimization recommendations
   */
  generateOptimizationRecommendations(): OptimizationRecommendation[] {
    const metrics = this.getCurrentMetrics()
    if (!metrics) return []

    const recommendations: OptimizationRecommendation[] = []

    // Page load time optimization
    if (metrics.pageLoadTime > 3000) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'Optimize Page Load Time',
        description: `Current page load time is ${(metrics.pageLoadTime / 1000).toFixed(1)}s, which exceeds the 3s target.`,
        impact: 'Improve user experience and SEO rankings',
        implementation: 'Implement code splitting, optimize images, enable compression, use CDN',
        estimatedSavings: {
          loadTime: metrics.pageLoadTime - 2000,
          bandwidth: 30
        }
      })
    }

    // Bundle size optimization
    if (metrics.bundleSize > 1024 * 1024) { // 1MB
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'Reduce Bundle Size',
        description: `Bundle size is ${(metrics.bundleSize / 1024 / 1024).toFixed(1)}MB, consider code splitting.`,
        impact: 'Faster initial load and reduced bandwidth usage',
        implementation: 'Implement dynamic imports, tree shaking, and remove unused dependencies',
        estimatedSavings: {
          loadTime: 1000,
          bandwidth: 40
        }
      })
    }

    // Cache hit rate optimization
    if (metrics.cacheHitRate < 0.7) {
      const cacheStats = getCacheStats()
      let implementation = 'Optimize cache TTL, implement better cache invalidation, use service workers'

      // Provide specific recommendations based on cache state
      if (cacheStats.totalEntries === 0) {
        implementation = 'Run cache warmup to preload frequently accessed data. Use cacheWarmupService.warmupAll()'
      } else if (cacheStats.expiredEntries > cacheStats.validEntries) {
        implementation = 'Increase cache TTL values and implement cache refresh strategies'
      } else if (cacheStats.hits < cacheStats.misses) {
        implementation = 'Implement cache warmup and preloading for critical data paths'
      }

      recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'Improve Cache Strategy',
        description: `Cache hit rate is ${(metrics.cacheHitRate * 100).toFixed(0)}%, below the 70% target.`,
        impact: 'Reduce server load and improve response times',
        implementation,
        estimatedSavings: {
          loadTime: 500,
          cost: 20
        }
      })
    }

    // Memory usage optimization
    if (metrics.memoryUsage > 100) { // 100MB
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'Optimize Memory Usage',
        description: `Memory usage is ${metrics.memoryUsage.toFixed(1)}MB, which may cause performance issues.`,
        impact: 'Prevent memory leaks and improve application stability',
        implementation: 'Fix memory leaks, optimize component lifecycle, implement virtual scrolling',
        estimatedSavings: {
          loadTime: 200
        }
      })
    }

    // Error rate optimization
    if (metrics.errorRate > 0.01) { // 1%
      recommendations.push({
        type: 'reliability',
        priority: 'critical',
        title: 'Reduce Error Rate',
        description: `Error rate is ${(metrics.errorRate * 100).toFixed(2)}%, above the 1% threshold.`,
        impact: 'Improve user experience and application reliability',
        implementation: 'Implement better error handling, add retry mechanisms, improve validation',
        estimatedSavings: {
          cost: 50
        }
      })
    }

    return recommendations
  }

  /**
   * Optimize bundle loading
   */
  optimizeBundleLoading() {
    if (typeof window === 'undefined') return

    // Preload critical resources
    const criticalResources = [
      '/api/user/profile',
      '/api/tenant/current',
      '/api/dashboard/widgets'
    ]

    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource
      link.as = 'fetch'
      link.setAttribute('crossorigin', 'anonymous')
      document.head.appendChild(link)
    })

    // Prefetch likely next pages
    const likelyPages = [
      '/dashboard',
      '/exhibitions',
      '/events',
      '/tasks'
    ]

    likelyPages.forEach(page => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = page
      document.head.appendChild(link)
    })
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect()
      this.performanceObserver = null
    }
    this.metricsBuffer = []
  }
}

export const productionOptimizationService = new ProductionOptimizationService()

// Export types
export type { PerformanceMetrics, OptimizationRecommendation, CacheStrategy }
