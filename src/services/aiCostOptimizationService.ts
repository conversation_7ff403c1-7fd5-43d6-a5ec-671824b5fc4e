/**
 * AI Cost Optimization Service for EVEXA
 * Implements comprehensive cost management, caching, and usage monitoring for AI features
 */

import { collection, doc, getDoc, setDoc, getDocs, query, where, orderBy, limit, Timestamp, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { dataCache } from './dataCache';
import { toast } from 'sonner';

export interface AICostConfig {
  enabled: boolean;
  maxDailyCost: number;
  maxMonthlyCost: number;
  alertThresholds: {
    daily: number;
    monthly: number;
  };
  cachingStrategy: {
    enabled: boolean;
    defaultTTL: number; // minutes
    maxCacheSize: number; // MB
    compressionEnabled: boolean;
  };
  rateLimiting: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  tieredFeatures: {
    free: string[];
    basic: string[];
    premium: string[];
    enterprise: string[];
  };
}

export interface AIUsageRecord {
  id: string;
  timestamp: Date;
  service: string;
  prompt: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost: number;
  cacheHit: boolean;
  responseTime: number;
  userId?: string;
  tenantId?: string;
  model: string;
  provider: string;
}

export interface AICostAnalytics {
  totalCost: number;
  dailyCost: number;
  monthlyCost: number;
  totalRequests: number;
  cacheHitRate: number;
  averageResponseTime: number;
  costByService: Record<string, number>;
  costByUser: Record<string, number>;
  costByTenant: Record<string, number>;
  topExpensiveQueries: AIUsageRecord[];
  costTrends: {
    date: string;
    cost: number;
    requests: number;
  }[];
}

export interface AICacheEntry {
  key: string;
  prompt: string;
  response: any;
  timestamp: Date;
  ttl: number;
  hits: number;
  service: string;
  compressed: boolean;
  size: number; // bytes
}

class AICostOptimizationService {
  private config: AICostConfig | null = null;
  private cache: Map<string, AICacheEntry> = new Map();
  private usageRecords: AIUsageRecord[] = [];
  private currentDailyCost = 0;
  private currentMonthlyCost = 0;
  private requestCounts = {
    minute: 0,
    hour: 0,
    day: 0
  };

  constructor() {
    this.initializeService();
  }

  /**
   * Initialize the cost optimization service
   */
  async initializeService(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.loadCacheFromStorage();
      await this.loadUsageData();
      this.startPeriodicCleanup();
      console.log('✅ AI Cost Optimization Service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize AI Cost Optimization Service:', error);
      this.config = this.getDefaultConfig();
    }
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): AICostConfig {
    return {
      enabled: true,
      maxDailyCost: 10.00, // $10 per day
      maxMonthlyCost: 200.00, // $200 per month
      alertThresholds: {
        daily: 8.00, // Alert at $8 daily
        monthly: 150.00 // Alert at $150 monthly
      },
      cachingStrategy: {
        enabled: true,
        defaultTTL: 60, // 1 hour
        maxCacheSize: 100, // 100MB
        compressionEnabled: true
      },
      rateLimiting: {
        requestsPerMinute: 30,
        requestsPerHour: 500,
        requestsPerDay: 2000
      },
      tieredFeatures: {
        free: ['virtualAssistant', 'basicInsights'],
        basic: ['virtualAssistant', 'basicInsights', 'budgetPrediction', 'leadScoring'],
        premium: ['virtualAssistant', 'basicInsights', 'budgetPrediction', 'leadScoring', 'contentGeneration', 'workflowAutomation'],
        enterprise: ['virtualAssistant', 'basicInsights', 'budgetPrediction', 'leadScoring', 'contentGeneration', 'workflowAutomation', 'anomalyDetection', 'performanceInsights']
      }
    };
  }

  /**
   * Load configuration from Firestore or localStorage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      // Try to load from localStorage first (works for all users) - only in browser
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        const localConfig = localStorage.getItem('evexa_ai_cost_config');
        if (localConfig) {
          this.config = JSON.parse(localConfig);
          return;
        }
      }

      // Fallback to Firestore for enterprise users
      try {
        const configDoc = await getDoc(doc(db, 'aiConfiguration', 'costOptimization'));
        if (configDoc.exists()) {
          this.config = configDoc.data() as AICostConfig;
        } else {
          this.config = this.getDefaultConfig();
          await this.saveConfiguration();
        }
      } catch (firestoreError) {
        console.warn('Firestore not available, using default config:', firestoreError);
        this.config = this.getDefaultConfig();
        this.saveConfiguration(); // This will save to localStorage
      }
    } catch (error) {
      console.error('Failed to load AI cost configuration:', error);
      this.config = this.getDefaultConfig();
    }
  }

  /**
   * Save configuration to localStorage and Firestore
   */
  async saveConfiguration(): Promise<void> {
    if (!this.config) return;

    try {
      // Always save to localStorage (works for all users) - only in browser
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        localStorage.setItem('evexa_ai_cost_config', JSON.stringify(this.config));
      }

      // Try to save to Firestore for enterprise users
      try {
        await setDoc(doc(db, 'aiConfiguration', 'costOptimization'), this.config);
      } catch (firestoreError) {
        console.warn('Could not save to Firestore, saved locally:', firestoreError);
      }
    } catch (error) {
      console.error('Failed to save AI cost configuration:', error);
    }
  }

  /**
   * Check if AI request is allowed based on cost and rate limits
   */
  async canMakeRequest(service: string, estimatedCost: number = 0.01): Promise<{
    allowed: boolean;
    reason?: string;
    suggestion?: string;
  }> {
    if (!this.config?.enabled) {
      return { allowed: false, reason: 'AI cost optimization is disabled' };
    }

    // Check daily cost limit
    if (this.currentDailyCost + estimatedCost > this.config.maxDailyCost) {
      return {
        allowed: false,
        reason: 'Daily cost limit exceeded',
        suggestion: 'Try again tomorrow or upgrade your plan'
      };
    }

    // Check monthly cost limit
    if (this.currentMonthlyCost + estimatedCost > this.config.maxMonthlyCost) {
      return {
        allowed: false,
        reason: 'Monthly cost limit exceeded',
        suggestion: 'Upgrade your plan or wait for next month'
      };
    }

    // Check rate limits
    if (this.requestCounts.minute >= this.config.rateLimiting.requestsPerMinute) {
      return {
        allowed: false,
        reason: 'Rate limit exceeded (per minute)',
        suggestion: 'Please wait a moment before making another request'
      };
    }

    if (this.requestCounts.hour >= this.config.rateLimiting.requestsPerHour) {
      return {
        allowed: false,
        reason: 'Rate limit exceeded (per hour)',
        suggestion: 'Please wait before making more requests'
      };
    }

    if (this.requestCounts.day >= this.config.rateLimiting.requestsPerDay) {
      return {
        allowed: false,
        reason: 'Daily request limit exceeded',
        suggestion: 'Try again tomorrow or upgrade your plan'
      };
    }

    return { allowed: true };
  }

  /**
   * Generate cache key for AI request
   */
  private generateCacheKey(service: string, prompt: string, context?: any): string {
    const contextStr = context ? JSON.stringify(context) : '';
    const combined = `${service}:${prompt}:${contextStr}`;
    
    // Create a hash of the combined string for consistent key generation
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return `ai_cache_${Math.abs(hash).toString(36)}`;
  }

  /**
   * Get cached response if available and valid
   */
  getCachedResponse(service: string, prompt: string, context?: any): any | null {
    if (!this.config?.cachingStrategy.enabled) {
      return null;
    }

    const cacheKey = this.generateCacheKey(service, prompt, context);
    const entry = this.cache.get(cacheKey);

    if (!entry) {
      return null;
    }

    // Check if cache entry is still valid
    const now = Date.now();
    const entryAge = now - entry.timestamp.getTime();
    const ttlMs = entry.ttl * 60 * 1000; // Convert minutes to milliseconds

    if (entryAge > ttlMs) {
      // Cache expired, remove it
      this.cache.delete(cacheKey);
      return null;
    }

    // Update hit count
    entry.hits++;
    
    console.log(`🎯 Cache hit for ${service}: ${cacheKey}`);
    return entry.response;
  }

  /**
   * Cache AI response
   */
  cacheResponse(service: string, prompt: string, response: any, context?: any, customTTL?: number): void {
    if (!this.config?.cachingStrategy.enabled) {
      return;
    }

    const cacheKey = this.generateCacheKey(service, prompt, context);
    const ttl = customTTL || this.config.cachingStrategy.defaultTTL;
    
    let responseData = response;
    let compressed = false;
    let size = JSON.stringify(response).length;

    // Compress large responses if enabled
    if (this.config.cachingStrategy.compressionEnabled && size > 1024) {
      try {
        responseData = this.compressData(response);
        compressed = true;
        size = JSON.stringify(responseData).length;
      } catch (error) {
        console.warn('Failed to compress cache entry:', error);
      }
    }

    const entry: AICacheEntry = {
      key: cacheKey,
      prompt: prompt.substring(0, 200), // Store truncated prompt for debugging
      response: responseData,
      timestamp: new Date(),
      ttl,
      hits: 0,
      service,
      compressed,
      size
    };

    this.cache.set(cacheKey, entry);
    
    // Check cache size and cleanup if needed
    this.cleanupCacheIfNeeded();
    
    console.log(`💾 Cached response for ${service}: ${cacheKey} (TTL: ${ttl}m, Size: ${size}b)`);
  }

  /**
   * Record AI usage for cost tracking
   */
  async recordUsage(record: Omit<AIUsageRecord, 'id' | 'timestamp'>): Promise<void> {
    const usageRecord: AIUsageRecord = {
      ...record,
      id: `usage_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    this.usageRecords.push(usageRecord);
    this.currentDailyCost += record.cost;
    this.currentMonthlyCost += record.cost;

    // Update request counts
    this.requestCounts.minute++;
    this.requestCounts.hour++;
    this.requestCounts.day++;

    // Save to localStorage for immediate access - only in browser
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      try {
        const existingRecords = JSON.parse(localStorage.getItem('evexa_ai_usage_records') || '[]');
        existingRecords.push(usageRecord);

        // Keep only last 1000 records to prevent storage bloat
        if (existingRecords.length > 1000) {
          existingRecords.splice(0, existingRecords.length - 1000);
        }

        localStorage.setItem('evexa_ai_usage_records', JSON.stringify(existingRecords));
      } catch (localError) {
        console.warn('Failed to save usage record locally:', localError);
      }
    }

    // Try to save to Firestore for enterprise users
    try {
      await addDoc(collection(db, 'aiUsageRecords'), {
        ...usageRecord,
        timestamp: Timestamp.fromDate(usageRecord.timestamp)
      });
    } catch (error) {
      console.warn('Could not save usage record to Firestore:', error);
    }

    // Check for cost alerts
    this.checkCostAlerts();
  }

  /**
   * Get cost analytics
   */
  async getCostAnalytics(): Promise<AICostAnalytics> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const dailyRecords = this.usageRecords.filter(r => r.timestamp >= today);
    const monthlyRecords = this.usageRecords.filter(r => r.timestamp >= thisMonth);

    const analytics: AICostAnalytics = {
      totalCost: this.usageRecords.reduce((sum, r) => sum + r.cost, 0),
      dailyCost: dailyRecords.reduce((sum, r) => sum + r.cost, 0),
      monthlyCost: monthlyRecords.reduce((sum, r) => sum + r.cost, 0),
      totalRequests: this.usageRecords.length,
      cacheHitRate: this.calculateCacheHitRate(),
      averageResponseTime: this.calculateAverageResponseTime(),
      costByService: this.groupCostByField('service'),
      costByUser: this.groupCostByField('userId'),
      costByTenant: this.groupCostByField('tenantId'),
      topExpensiveQueries: this.getTopExpensiveQueries(10),
      costTrends: this.generateCostTrends(30) // Last 30 days
    };

    return analytics;
  }

  /**
   * Get analytics (alias for getCostAnalytics for API compatibility)
   */
  getAnalytics(): Promise<AICostAnalytics> {
    return this.getCostAnalytics();
  }

  /**
   * Get usage history for detailed analysis
   */
  getUsageHistory(limit: number = 100): AIUsageRecord[] {
    return this.usageRecords
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Reset analytics data (for testing/debugging)
   */
  resetAnalytics(): void {
    this.usageRecords = [];
    this.currentDailyCost = 0;
    this.currentMonthlyCost = 0;
    this.saveUsageRecords();
    console.log('📊 Analytics data reset');
  }

  /**
   * Check for cost alerts and notify if thresholds are exceeded
   */
  private checkCostAlerts(): void {
    if (!this.config) return;

    if (this.currentDailyCost >= this.config.alertThresholds.daily) {
      toast.warning(`Daily AI cost alert: $${this.currentDailyCost.toFixed(2)} / $${this.config.maxDailyCost.toFixed(2)}`);
    }

    if (this.currentMonthlyCost >= this.config.alertThresholds.monthly) {
      toast.warning(`Monthly AI cost alert: $${this.currentMonthlyCost.toFixed(2)} / $${this.config.maxMonthlyCost.toFixed(2)}`);
    }
  }

  /**
   * Compress data for caching
   */
  private compressData(data: any): any {
    // Simple compression - in production, use a proper compression library
    const jsonStr = JSON.stringify(data);
    return {
      compressed: true,
      data: jsonStr // In production, use LZ-string or similar
    };
  }

  /**
   * Decompress cached data
   */
  private decompressData(compressedData: any): any {
    if (compressedData.compressed) {
      return JSON.parse(compressedData.data);
    }
    return compressedData;
  }

  /**
   * Clean up cache if size limit exceeded
   */
  private cleanupCacheIfNeeded(): void {
    if (!this.config) return;

    const maxSizeBytes = this.config.cachingStrategy.maxCacheSize * 1024 * 1024;
    let currentSize = 0;

    // Calculate current cache size
    for (const entry of this.cache.values()) {
      currentSize += entry.size;
    }

    if (currentSize > maxSizeBytes) {
      // Remove least recently used entries
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());

      while (currentSize > maxSizeBytes * 0.8 && entries.length > 0) {
        const [key, entry] = entries.shift()!;
        this.cache.delete(key);
        currentSize -= entry.size;
      }

      console.log(`🧹 Cache cleanup completed. Size reduced to ${(currentSize / 1024 / 1024).toFixed(2)}MB`);
    }
  }

  /**
   * Calculate cache hit rate
   */
  private calculateCacheHitRate(): number {
    const totalHits = Array.from(this.cache.values()).reduce((sum, entry) => sum + entry.hits, 0);
    const totalRequests = this.usageRecords.length + totalHits;
    return totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
  }

  /**
   * Calculate average response time
   */
  private calculateAverageResponseTime(): number {
    if (this.usageRecords.length === 0) return 0;
    const totalTime = this.usageRecords.reduce((sum, r) => sum + r.responseTime, 0);
    return totalTime / this.usageRecords.length;
  }

  /**
   * Group costs by field
   */
  private groupCostByField(field: keyof AIUsageRecord): Record<string, number> {
    const grouped: Record<string, number> = {};
    
    for (const record of this.usageRecords) {
      const key = (record[field] as string) || 'unknown';
      grouped[key] = (grouped[key] || 0) + record.cost;
    }
    
    return grouped;
  }

  /**
   * Get top expensive queries
   */
  private getTopExpensiveQueries(limit: number): AIUsageRecord[] {
    return [...this.usageRecords]
      .sort((a, b) => b.cost - a.cost)
      .slice(0, limit);
  }

  /**
   * Generate cost trends
   */
  private generateCostTrends(days: number): { date: string; cost: number; requests: number; }[] {
    const trends: { date: string; cost: number; requests: number; }[] = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayRecords = this.usageRecords.filter(r => 
        r.timestamp.toISOString().split('T')[0] === dateStr
      );

      trends.push({
        date: dateStr,
        cost: dayRecords.reduce((sum, r) => sum + r.cost, 0),
        requests: dayRecords.length
      });
    }

    return trends;
  }

  /**
   * Load cache from storage
   */
  private async loadCacheFromStorage(): Promise<void> {
    try {
      const cacheData = await dataCache.get('ai_cost_cache', async () => ({}));
      // Implementation would load from persistent storage
    } catch (error) {
      console.warn('Failed to load AI cache from storage:', error);
    }
  }

  /**
   * Load usage data from localStorage and Firestore
   */
  private async loadUsageData(): Promise<void> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Load from localStorage first - only in browser
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        try {
          const localRecords = JSON.parse(localStorage.getItem('evexa_ai_usage_records') || '[]');
          this.usageRecords = localRecords.map((record: any) => ({
            ...record,
            timestamp: new Date(record.timestamp)
          }));
        } catch (localError) {
          console.warn('Failed to load local usage data:', localError);
          this.usageRecords = [];
        }
      } else {
        this.usageRecords = [];
      }

      // Try to load from Firestore for additional data
      try {
        const usageQuery = query(
          collection(db, 'aiUsageRecords'),
          where('timestamp', '>=', Timestamp.fromDate(thisMonth)),
          orderBy('timestamp', 'desc'),
          limit(1000)
        );

        const snapshot = await getDocs(usageQuery);
        const firestoreRecords = snapshot.docs.map(doc => ({
          ...doc.data(),
          id: doc.id,
          timestamp: doc.data().timestamp.toDate()
        })) as AIUsageRecord[];

        // Merge with local records (avoid duplicates)
        const allRecords = [...this.usageRecords];
        for (const record of firestoreRecords) {
          if (!allRecords.find(r => r.id === record.id)) {
            allRecords.push(record);
          }
        }
        this.usageRecords = allRecords;
      } catch (firestoreError) {
        console.warn('Could not load Firestore usage data:', firestoreError);
      }

      // Calculate current costs
      this.currentDailyCost = this.usageRecords
        .filter(r => r.timestamp >= today)
        .reduce((sum, r) => sum + r.cost, 0);

      this.currentMonthlyCost = this.usageRecords
        .filter(r => r.timestamp >= thisMonth)
        .reduce((sum, r) => sum + r.cost, 0);

    } catch (error) {
      console.warn('Failed to load usage data:', error);
      this.usageRecords = [];
      this.currentDailyCost = 0;
      this.currentMonthlyCost = 0;
    }
  }

  /**
   * Start periodic cleanup
   */
  private startPeriodicCleanup(): void {
    // Reset rate limiting counters
    setInterval(() => {
      this.requestCounts.minute = 0;
    }, 60 * 1000); // Every minute

    setInterval(() => {
      this.requestCounts.hour = 0;
    }, 60 * 60 * 1000); // Every hour

    setInterval(() => {
      this.requestCounts.day = 0;
      this.currentDailyCost = 0;
    }, 24 * 60 * 60 * 1000); // Every day

    // Monthly reset
    setInterval(() => {
      this.currentMonthlyCost = 0;
    }, 30 * 24 * 60 * 60 * 1000); // Every 30 days (approximate)

    // Cache cleanup
    setInterval(() => {
      this.cleanupCacheIfNeeded();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Update configuration
   */
  async updateConfiguration(newConfig: Partial<AICostConfig>): Promise<void> {
    if (!this.config) return;

    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();
    
    toast.success('AI cost configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfiguration(): AICostConfig | null {
    return this.config;
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear();
    toast.success('AI cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    entries: number;
    hitRate: number;
    totalHits: number;
  } {
    let totalSize = 0;
    let totalHits = 0;

    for (const entry of this.cache.values()) {
      totalSize += entry.size;
      totalHits += entry.hits;
    }

    return {
      size: totalSize,
      entries: this.cache.size,
      hitRate: this.calculateCacheHitRate(),
      totalHits
    };
  }
}

// Export singleton instance
export const aiCostOptimizationService = new AICostOptimizationService();
