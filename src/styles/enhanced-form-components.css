/* Enhanced Form Components - Global Styling */

/* Select Component Enhancements - More Specific Targeting */
[data-radix-select-trigger],
.select-trigger-enhanced {
  @apply bg-background border-border hover:bg-accent/50 hover:border-border transition-all duration-200 !important;
}

[data-radix-select-trigger]:focus,
.select-trigger-enhanced:focus {
  @apply ring-2 ring-primary/20 border-primary/50 !important;
}

[data-radix-select-content] {
  @apply bg-popover border-border shadow-lg !important;
}

[data-radix-select-item] {
  @apply hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground !important;
}

/* Additional Select Styling */
.select-trigger-enhanced[data-state="open"] {
  @apply ring-2 ring-primary/20 border-primary/50 !important;
}

/* Enhanced Select Trigger Styling */
.select-trigger-enhanced {
  @apply bg-background border-border hover:bg-accent/50 hover:border-border transition-all duration-200 !important;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50 !important;
  @apply text-foreground !important;
}

/* Ensure Select placeholder and value text is visible */
.select-trigger-enhanced [data-placeholder] {
  @apply text-muted-foreground !important;
}

.select-trigger-enhanced [data-radix-select-value] {
  @apply text-foreground !important;
}

/* Form Field Container Enhancements */
.form-field-card {
  @apply bg-card border border-border p-5 rounded-lg shadow-sm;
  @apply hover:shadow-md hover:border-border transition-all duration-200;
}

.form-field-card-primary {
  @apply bg-card border border-primary/30 p-6 rounded-lg;
  @apply shadow-sm hover:shadow-md transition-all duration-200;
  @apply relative;
}

.form-field-card-primary::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg pointer-events-none;
}

.form-field-card-secondary {
  @apply bg-card border border-blue-500/30 p-6 rounded-xl shadow-sm;
  @apply relative;
}

.form-field-card-secondary::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-xl pointer-events-none;
}

.form-field-card-success {
  @apply bg-card border border-green-500/30 p-6 rounded-xl shadow-sm;
  @apply relative;
}

.form-field-card-success::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-xl pointer-events-none;
}

/* Ensure content is above the pseudo-elements */
.form-field-card-primary > *,
.form-field-card-secondary > *,
.form-field-card-success > * {
  @apply relative z-10;
}

/* Enhanced Input Styling */
.input-enhanced {
  @apply h-11 bg-background/80 border-border/60 hover:bg-background hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200;
}

.input-enhanced-large {
  @apply h-12 text-lg bg-background/50 border-border/60 hover:bg-background hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200;
}

/* Enhanced Textarea Styling */
.textarea-enhanced {
  @apply bg-background/80 border-border/60 hover:bg-background hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200;
  @apply resize-none;
}

/* Enhanced Button Styling for Forms */
.button-enhanced-outline {
  @apply bg-background/80 hover:bg-background border-border/60 hover:border-border;
  @apply transition-all duration-200;
}

/* Enhanced Popover Styling */
[data-radix-popover-content] {
  @apply bg-background/95 backdrop-blur-sm border-border/60 shadow-xl;
}

/* Enhanced Checkbox Styling */
.checkbox-enhanced {
  @apply border-border/60 hover:border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary;
  @apply transition-all duration-200;
}

/* Form Section Headers */
.form-section-header {
  @apply flex items-center text-lg font-semibold mb-4;
}

.form-section-header-icon {
  @apply p-2 rounded-lg mr-3;
}

.form-section-header-primary .form-section-header-icon {
  @apply bg-primary/10 text-primary;
}

.form-section-header-blue .form-section-header-icon {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
}

.form-section-header-green .form-section-header-icon {
  @apply bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400;
}

/* Enhanced Form Labels */
.form-label-enhanced {
  @apply text-base font-medium text-foreground/90;
}

.form-label-enhanced-large {
  @apply text-lg font-semibold text-foreground;
}

/* Enhanced Form Descriptions */
.form-description-enhanced {
  @apply text-sm text-muted-foreground/80;
}

/* Grid Layouts for Forms */
.form-grid-2 {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-grid-3 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* Form Container */
.form-container-enhanced {
  @apply space-y-6 max-w-4xl mx-auto;
}

/* Dark mode specific enhancements */
.dark [data-radix-select-trigger],
.dark .select-trigger-enhanced {
  @apply bg-background border-border hover:bg-accent/30 !important;
}

.dark [data-radix-select-content] {
  @apply bg-popover border-border !important;
}

.dark [data-radix-select-item] {
  @apply text-foreground hover:bg-accent hover:text-accent-foreground !important;
}

.dark .form-field-card {
  @apply bg-card/30 border-border/40;
}

/* Force better contrast for select values */
[data-radix-select-value] {
  @apply text-foreground !important;
}

.dark [data-radix-select-value] {
  @apply text-foreground !important;
}
