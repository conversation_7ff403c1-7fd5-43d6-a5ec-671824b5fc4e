/**
 * EVEXA UI Consistency Fixes
 *
 * ONLY ESSENTIAL FIXES - Sidebar alignment only
 * All other fixes were too aggressive and breaking existing components
 */

/* ========================================
   SIDEBAR ALIGNMENT FIXES - CRITICAL
   ======================================== */

/* NUCLEAR OPTION - Force sidebar alignment with maximum specificity */

/* Target the exact button structure from the settings page */
html body * [data-sidebar="menu-button"] span,
html body * button[data-sidebar="menu-button"] span,
html body * .peer\/menu-button span {
  text-align: left !important;
  flex: none !important; /* Don't let it grow to fill space */
  margin-left: 0.5rem !important; /* Small gap after icon */
  margin-right: 0 !important;
  justify-self: flex-start !important;
}

/* Force the button container alignment */
html body * [data-sidebar="menu-button"],
html body * button[data-sidebar="menu-button"],
html body * .peer\/menu-button {
  justify-content: flex-start !important;
  display: flex !important;
  align-items: center !important;
  text-align: left !important;
}

/* Target any flexbox centering that might be applied */
html body * [data-sidebar="menu-button"]:not(.justify-center),
html body * button[data-sidebar="menu-button"]:not(.justify-center) {
  justify-content: flex-start !important;
}

/* Override any CSS Grid centering */
html body * [data-sidebar="menu-button"] {
  place-items: start !important;
  place-content: start !important;
  justify-items: start !important;
}

/* Force icon positioning */
html body * [data-sidebar="menu-button"] > div:first-child,
html body * [data-sidebar="menu-button"] > svg:first-child {
  margin-right: 0 !important;
  margin-left: 0 !important;
  flex-shrink: 0 !important;
}

/* Keep chevron on right */
html body * [data-sidebar="menu-button"] .ml-auto,
html body * [data-sidebar="menu-button"] [class*="ml-auto"] {
  margin-left: auto !important;
  flex-shrink: 0 !important;
}

/* ===== BUTTON FIXES ===== */

/* Ensure buttons are not cut off and have proper styling */
button[type="submit"] {
  min-width: fit-content !important;
  white-space: nowrap !important;
  overflow: visible !important;
}

/* Fix button containers that might be cutting off buttons */
.flex.justify-end {
  overflow: visible !important;
}

/* Ensure form containers don't cut off buttons */
form {
  overflow: visible !important;
}

/* Fix any card content that might be cutting off buttons */
[class*="CardContent"] {
  overflow: visible !important;
}

/* ===== SIDEBAR SELECTED TEXT READABILITY ===== */

/* Fix selected/active sidebar items text contrast */
[data-sidebar="menu-button"][data-active="true"],
[data-sidebar="menu-button"].bg-sidebar-accent,
.peer\/menu-button[data-active="true"],
.peer\/menu-button.bg-sidebar-accent {
  color: var(--sidebar-accent-foreground) !important;
  background-color: var(--sidebar-accent) !important;
}

/* Ensure text is readable in selected state */
[data-sidebar="menu-button"][data-active="true"] span,
[data-sidebar="menu-button"].bg-sidebar-accent span,
.peer\/menu-button[data-active="true"] span,
.peer\/menu-button.bg-sidebar-accent span {
  color: var(--sidebar-accent-foreground) !important;
  font-weight: 500 !important;
}

/* Fix hover state readability */
[data-sidebar="menu-button"]:hover span {
  color: var(--sidebar-foreground) !important;
}

/* Ensure icons are also readable */
[data-sidebar="menu-button"][data-active="true"] svg,
[data-sidebar="menu-button"].bg-sidebar-accent svg {
  color: var(--sidebar-accent-foreground) !important;
}

/* ===== DROPDOWN MENU TEXT READABILITY ===== */

/* Simple fix - just make dropdown text white for dark theme */
[data-radix-dropdown-menu-content] {
  background-color: hsl(var(--popover)) !important;
}


