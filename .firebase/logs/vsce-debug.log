[debug] [2025-07-09T06:50:57.209Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-09T06:50:58.297Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-09T06:50:58.298Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-09T06:50:58.298Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-09T06:50:58.299Z] Checked if tokens are valid: false, expires at: 1751985852098
[debug] [2025-07-09T06:50:58.299Z] Checked if tokens are valid: false, expires at: 1751985852098
[debug] [2025-07-09T06:50:58.299Z] > refreshing access token with scopes: []
[debug] [2025-07-09T06:50:58.300Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T06:50:58.300Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T06:50:58.303Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-07-09T06:50:58.600Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-09T06:50:58.600Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T06:50:58.610Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-09T06:50:58.611Z] Checked if tokens are valid: true, expires at: 1752047457600
[debug] [2025-07-09T06:50:58.611Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-09T06:50:59.234Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-09T06:50:59.234Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-07-10T06:56:42.149Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-10T06:56:42.597Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-10T06:56:42.598Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-10T06:56:42.598Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-10T06:56:42.599Z] Checked if tokens are valid: false, expires at: 1752047457600
[debug] [2025-07-10T06:56:42.599Z] Checked if tokens are valid: false, expires at: 1752047457600
[debug] [2025-07-10T06:56:42.599Z] > refreshing access token with scopes: []
[debug] [2025-07-10T06:56:42.600Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-10T06:56:42.600Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-10T06:56:42.603Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-07-10T06:56:42.908Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-10T06:56:42.909Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-10T06:56:42.919Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-10T06:56:42.920Z] Checked if tokens are valid: true, expires at: 1752134201909
[debug] [2025-07-10T06:56:42.920Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-10T06:56:44.236Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-10T06:56:44.236Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-07-11T14:46:52.750Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-11T14:46:57.391Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-11T14:46:57.392Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-11T14:46:57.392Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-11T14:46:57.393Z] Checked if tokens are valid: false, expires at: 1752134201909
[debug] [2025-07-11T14:46:57.393Z] Checked if tokens are valid: false, expires at: 1752134201909
[debug] [2025-07-11T14:46:57.393Z] > refreshing access token with scopes: []
[debug] [2025-07-11T14:46:57.394Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-11T14:46:57.394Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-11T14:46:57.396Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-07-11T14:46:59.732Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-11T14:46:59.732Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-11T14:46:59.756Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-11T14:46:59.757Z] Checked if tokens are valid: true, expires at: 1752248818732
[debug] [2025-07-11T14:46:59.757Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-11T14:47:04.755Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-11T14:47:04.755Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-07-11T18:18:08.564Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-11T18:18:11.749Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-11T18:18:11.749Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-11T18:18:11.750Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-11T18:18:11.751Z] Checked if tokens are valid: false, expires at: 1752254805369
[debug] [2025-07-11T18:18:11.751Z] Checked if tokens are valid: false, expires at: 1752254805369
[debug] [2025-07-11T18:18:11.751Z] > refreshing access token with scopes: []
[debug] [2025-07-11T18:18:11.751Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-11T18:18:11.751Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-11T18:18:11.754Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-07-11T18:18:12.575Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-11T18:18:12.575Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-11T18:18:12.619Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-11T18:18:12.620Z] Checked if tokens are valid: true, expires at: 1752261491575
[debug] [2025-07-11T18:18:12.621Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-11T18:18:16.980Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-11T18:18:16.981Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-07-12T07:28:32.959Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-12T07:28:33.177Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-12T07:28:33.178Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-12T07:28:33.178Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-12T07:28:33.179Z] Checked if tokens are valid: true, expires at: 1752308911830
[debug] [2025-07-12T07:28:33.179Z] Checked if tokens are valid: true, expires at: 1752308911830
[debug] [2025-07-12T07:28:33.179Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-12T07:28:33.180Z] Checked if tokens are valid: true, expires at: 1752308911830
[debug] [2025-07-12T07:28:33.180Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-12T07:28:33.890Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-12T07:28:33.890Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-07-12T11:34:09.599Z] [Firebase Plugin] Value of process.env.MONOSPACE_ENV: undefined
[debug] [2025-07-12T11:34:09.600Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-12T11:34:09.602Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-12T11:34:09.602Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-12T11:34:09.603Z] Checked if tokens are valid: false, expires at: 1752308911830
[debug] [2025-07-12T11:34:09.603Z] Checked if tokens are valid: false, expires at: 1752308911830
[debug] [2025-07-12T11:34:09.603Z] > refreshing access token with scopes: []
[debug] [2025-07-12T11:34:09.604Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-12T11:34:09.604Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-12T11:34:09.606Z] [Firebase Plugin] Value of process.env.MONOSPACE_ENV: undefined
[debug] [2025-07-12T11:34:09.606Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-12T11:34:09.606Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-12T11:34:09.606Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-12T11:34:09.606Z] Checked if tokens are valid: false, expires at: 1752308911830
[debug] [2025-07-12T11:34:09.606Z] Checked if tokens are valid: false, expires at: 1752308911830
[debug] [2025-07-12T11:34:09.606Z] > refreshing access token with scopes: []
[debug] [2025-07-12T11:34:09.606Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-12T11:34:09.606Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-12T11:34:10.706Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-12T11:34:10.707Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-12T11:34:10.735Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-12T11:34:10.736Z] Checked if tokens are valid: true, expires at: 1752323649707
[debug] [2025-07-12T11:34:10.739Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-12T11:34:10.976Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-12T11:34:10.976Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-12T11:34:10.993Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-12T11:34:10.994Z] Checked if tokens are valid: true, expires at: 1752323649977
[debug] [2025-07-12T11:34:10.995Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-12T11:34:14.078Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-12T11:34:14.078Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-07-12T11:34:14.288Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-12T11:34:14.288Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[info] i  dataconnect: downloading dataconnect-emulator-2.8.0... {"metadata":{"emulator":{"name":"dataconnect"},"message":"downloading dataconnect-emulator-2.8.0..."}}
[debug] [2025-07-12T11:34:55.840Z] >>> [apiv2][query] GET https://storage.googleapis.com/firemat-preview-drop/emulator/dataconnect-emulator-macos-v2.8.0 
[debug] [2025-07-12T11:34:57.060Z] <<< [apiv2][status] GET https://storage.googleapis.com/firemat-preview-drop/emulator/dataconnect-emulator-macos-v2.8.0 200
[debug] [2025-07-12T11:34:57.061Z] <<< [apiv2][body] GET https://storage.googleapis.com/firemat-preview-drop/emulator/dataconnect-emulator-macos-v2.8.0 [stream]
[debug] [2025-07-12T11:35:02.956Z] 'fdc build' failed with error: Unable to build your Data Connect schema and connectors (exit code 1): E0712 15:35:02.949152   50341 cmd.go:43] Error loading data connect config: could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"'fdc build' failed with error: Unable to build your Data Connect schema and connectors (exit code 1): E0712 15:35:02.949152   50341 cmd.go:43] Error loading data connect config: could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:35:02.966Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-12T11:35:02.966Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-12T11:35:02.966Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-12T11:35:03.096Z] E0712 15:35:03.095196   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:03.095196   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:35:03.099Z] E0712 15:35:03.099070   50354 watch.go:76] watchAllDir unable to walk dir `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`: on path "/Users/<USER>/Downloads/EVEXA-300625/dataconnect" (DirEntry: <nil>): lstat /Users/<USER>/Downloads/EVEXA-300625/dataconnect: no such file or directory
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:03.099070   50354 watch.go:76] watchAllDir unable to walk dir `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`: on path \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\" (DirEntry: <nil>): lstat /Users/<USER>/Downloads/EVEXA-300625/dataconnect: no such file or directory\n"}}
[debug] [2025-07-12T11:35:03.102Z] I0712 15:35:03.102394   50354 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0712 15:35:03.102394   50354 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[error] [Firebase Plugin] error:  {}
[debug] [2025-07-12T11:35:13.100Z] E0712 15:35:13.100362   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:13.100362   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:35:23.100Z] E0712 15:35:23.100081   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:23.100081   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:35:33.099Z] E0712 15:35:33.099329   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:33.099329   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:35:43.099Z] E0712 15:35:43.099429   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:43.099429   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:35:53.100Z] E0712 15:35:53.100385   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:35:53.100385   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:36:03.100Z] E0712 15:36:03.100411   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:36:03.100411   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:36:13.098Z] E0712 15:36:13.098449   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:36:13.098449   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:36:23.100Z] E0712 15:36:23.099832   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:36:23.099832   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:36:33.099Z] E0712 15:36:33.099347   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:36:33.099347   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:36:43.098Z] E0712 15:36:43.098518   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:36:43.098518   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:36:53.099Z] E0712 15:36:53.099257   50354 load.go:23] Could not load config from "/Users/<USER>/Downloads/EVEXA-300625/dataconnect": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"E0712 15:36:53.099257   50354 load.go:23] Could not load config from \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect\": could not find dataconnect.yaml in `/Users/<USER>/Downloads/EVEXA-300625/dataconnect`\n"}}
[debug] [2025-07-12T11:37:03.113Z] I0712 15:37:03.113160   50354 load.go:37] Reloading schema and connectors...
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0712 15:37:03.113160   50354 load.go:37] Reloading schema and connectors...\n"}}
[debug] [2025-07-12T11:37:03.141Z] I0712 15:37:03.141003   50354 control.go:84] [/emulator/tradeshow-os-service fde8] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0712 15:37:03.141003   50354 control.go:84] [/emulator/tradeshow-os-service fde8] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-12T11:37:03.156Z] I0712 15:37:03.156561   50354 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0712 15:37:03.156561   50354 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \n"}}
[debug] [2025-07-12T11:37:03.156Z] I0712 15:37:03.156681   50354 load.go:115] Finished reloading
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0712 15:37:03.156681   50354 load.go:115] Finished reloading\n"}}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-13T09:58:12.503Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Checked firebase-tools, is up to date!
[debug] [2025-07-13T09:58:14.517Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-13T09:58:14.518Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-13T09:58:14.518Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-13T09:58:14.519Z] Checked if tokens are valid: false, expires at: 1752326606102
[debug] [2025-07-13T09:58:14.519Z] Checked if tokens are valid: false, expires at: 1752326606102
[debug] [2025-07-13T09:58:14.519Z] > refreshing access token with scopes: []
[debug] [2025-07-13T09:58:14.520Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-13T09:58:14.520Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-13T09:58:14.523Z] [Firebase Plugin] VSCode notification server listening on port 40001
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[info] i  dataconnect: downloading dataconnect-emulator-2.8.0... {"metadata":{"emulator":{"name":"dataconnect"},"message":"downloading dataconnect-emulator-2.8.0..."}}
[debug] [2025-07-13T09:58:14.615Z] >>> [apiv2][query] GET https://storage.googleapis.com/firemat-preview-drop/emulator/dataconnect-emulator-macos-v2.8.0 
[debug] [2025-07-13T09:58:14.884Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-13T09:58:14.884Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-13T09:58:14.937Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-13T09:58:14.937Z] Checked if tokens are valid: true, expires at: 1752404293884
[debug] [2025-07-13T09:58:14.937Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-13T09:58:15.119Z] <<< [apiv2][status] GET https://storage.googleapis.com/firemat-preview-drop/emulator/dataconnect-emulator-macos-v2.8.0 200
[debug] [2025-07-13T09:58:15.120Z] <<< [apiv2][body] GET https://storage.googleapis.com/firemat-preview-drop/emulator/dataconnect-emulator-macos-v2.8.0 [stream]
[debug] [2025-07-13T09:58:15.575Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-13T09:58:15.575Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] i  dataconnect: Removing outdated emulator files: dataconnect-emulator-2.9.1 {"metadata":{"emulator":{"name":"dataconnect"},"message":"Removing outdated emulator files: dataconnect-emulator-2.9.1"}}
[debug] [2025-07-13T09:58:29.130Z] I0713 13:58:29.120099    3002 control.go:84] [/emulator/tradeshow-os-service a7c5] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.120099    3002 control.go:84] [/emulator/tradeshow-os-service a7c5] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-13T09:58:29.130Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-13T09:58:29.130Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-13T09:58:29.130Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-13T09:58:29.263Z] I0713 13:58:29.262125    3032 load.go:37] Reloading schema and connectors...
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.262125    3032 load.go:37] Reloading schema and connectors...\n"}}
[debug] [2025-07-13T09:58:29.282Z] I0713 13:58:29.282659    3032 control.go:84] [/emulator/tradeshow-os-service f6f5] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/queries.gql [1802B] connector/mutations.gql [1233B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.282659    3032 control.go:84] [/emulator/tradeshow-os-service f6f5] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/queries.gql [1802B] connector/mutations.gql [1233B] \n"}}
[debug] [2025-07-13T09:58:29.295Z] I0713 13:58:29.295671    3032 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.295671    3032 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \n"}}
[debug] [2025-07-13T09:58:29.296Z] I0713 13:58:29.295772    3032 load.go:115] Finished reloading
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.295772    3032 load.go:115] Finished reloading\n"}}
[debug] [2025-07-13T09:58:29.303Z] I0713 13:58:29.302860    3032 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.302860    3032 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-13T09:58:29.413Z] I0713 13:58:29.413841    3032 engine.go:170] [/emulator/tradeshow-os-service f6f5] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0713 13:58:29.413841    3032 engine.go:170] [/emulator/tradeshow-os-service f6f5] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-14T07:37:39.970Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Checked firebase-tools, is up to date!
[debug] [2025-07-14T07:37:51.714Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-14T07:37:51.715Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-14T07:37:51.715Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-14T07:37:51.716Z] Checked if tokens are valid: false, expires at: 1752404293884
[debug] [2025-07-14T07:37:51.716Z] Checked if tokens are valid: false, expires at: 1752404293884
[debug] [2025-07-14T07:37:51.716Z] > refreshing access token with scopes: []
[debug] [2025-07-14T07:37:51.717Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-14T07:37:51.717Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-14T07:37:51.720Z] [Firebase Plugin] VSCode notification server listening on port 40001
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[debug] [2025-07-14T07:37:52.210Z] I0714 11:37:52.199256    2437 control.go:84] [/emulator/tradeshow-os-service 8dac] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.199256    2437 control.go:84] [/emulator/tradeshow-os-service 8dac] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-14T07:37:52.210Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-14T07:37:52.210Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-14T07:37:52.210Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-14T07:37:52.350Z] I0714 11:37:52.348350    2454 load.go:37] Reloading schema and connectors...
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.348350    2454 load.go:37] Reloading schema and connectors...\n"}}
[debug] [2025-07-14T07:37:52.370Z] I0714 11:37:52.370490    2454 control.go:84] [/emulator/tradeshow-os-service cf0c] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.370490    2454 control.go:84] [/emulator/tradeshow-os-service cf0c] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-14T07:37:52.386Z] I0714 11:37:52.386366    2454 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.386366    2454 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \n"}}
[debug] [2025-07-14T07:37:52.387Z] I0714 11:37:52.386909    2454 load.go:115] Finished reloading
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.386909    2454 load.go:115] Finished reloading\n"}}
[debug] [2025-07-14T07:37:52.395Z] I0714 11:37:52.395316    2454 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.395316    2454 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-14T07:37:52.993Z] I0714 11:37:52.993276    2454 engine.go:170] [/emulator/tradeshow-os-service cf0c] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0714 11:37:52.993276    2454 engine.go:170] [/emulator/tradeshow-os-service cf0c] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[debug] [2025-07-14T07:37:56.050Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-14T07:37:56.050Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-14T07:37:56.064Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-14T07:37:56.064Z] Checked if tokens are valid: true, expires at: 1752482275050
[debug] [2025-07-14T07:37:56.066Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-14T07:37:58.580Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-14T07:37:58.580Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-15T09:34:02.141Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Checked firebase-tools, is up to date!
[debug] [2025-07-15T09:34:03.938Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-15T09:34:03.938Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-15T09:34:03.938Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-15T09:34:03.939Z] Checked if tokens are valid: false, expires at: 1752482275050
[debug] [2025-07-15T09:34:03.939Z] Checked if tokens are valid: false, expires at: 1752482275050
[debug] [2025-07-15T09:34:03.940Z] > refreshing access token with scopes: []
[debug] [2025-07-15T09:34:03.940Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-15T09:34:03.940Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-15T09:34:03.943Z] [Firebase Plugin] VSCode notification server listening on port 40001
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[debug] [2025-07-15T09:34:04.453Z] I0715 13:34:04.440617    2478 control.go:84] [/emulator/tradeshow-os-service a01b] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:04.440617    2478 control.go:84] [/emulator/tradeshow-os-service a01b] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-15T09:34:04.453Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-15T09:34:04.454Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-15T09:34:04.454Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-15T09:34:04.632Z] I0715 13:34:04.631398    2544 load.go:37] Reloading schema and connectors...
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:04.631398    2544 load.go:37] Reloading schema and connectors...\n"}}
[debug] [2025-07-15T09:34:04.654Z] I0715 13:34:04.654671    2544 control.go:84] [/emulator/tradeshow-os-service 0afd] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:04.654671    2544 control.go:84] [/emulator/tradeshow-os-service 0afd] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-15T09:34:04.670Z] I0715 13:34:04.670434    2544 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:04.670434    2544 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \n"}}
[debug] [2025-07-15T09:34:04.670Z] I0715 13:34:04.670733    2544 load.go:115] Finished reloading
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:04.670733    2544 load.go:115] Finished reloading\n"}}
[debug] [2025-07-15T09:34:04.679Z] I0715 13:34:04.679108    2544 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:04.679108    2544 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-15T09:34:12.313Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-15T09:34:12.313Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-15T09:34:12.362Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-15T09:34:12.362Z] Checked if tokens are valid: true, expires at: 1752575651313
[debug] [2025-07-15T09:34:12.363Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-15T09:34:13.675Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-15T09:34:13.675Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-07-15T09:34:34.947Z] I0715 13:34:34.946905    2544 engine.go:170] [/emulator/tradeshow-os-service 0afd] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0715 13:34:34.946905    2544 engine.go:170] [/emulator/tradeshow-os-service 0afd] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-17T09:55:19.263Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Unable to check firebase-tools installation: TypeError: fetch failed
[debug] [2025-07-17T09:55:19.278Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-17T09:55:19.279Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-17T09:55:19.279Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-17T09:55:19.280Z] Checked if tokens are valid: false, expires at: 1752575651313
[debug] [2025-07-17T09:55:19.280Z] Checked if tokens are valid: false, expires at: 1752575651313
[debug] [2025-07-17T09:55:19.280Z] > refreshing access token with scopes: []
[debug] [2025-07-17T09:55:19.281Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-17T09:55:19.281Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-17T09:55:19.284Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-07-17T09:55:19.288Z] *** [apiv2] error from fetch(https://www.googleapis.com/oauth2/v3/token, {"headers":{},"method":"POST","body":{"_overheadLength":545,"_valueLength":212,"_valuesToMeasure":[],"writable":false,"readable":true,"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"_released":true,"_streams":[],"_currentStream":null,"_insideLoop":false,"_pendingNext":false,"_boundary":"--------------------------535028273380178450112759","_events":{},"_eventsCount":1}}): FetchError: request to https://www.googleapis.com/oauth2/v3/token failed, reason: getaddrinfo ENOTFOUND www.googleapis.com
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] [Firebase Plugin] requireAuth error: Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[debug] [2025-07-17T09:55:19.683Z] I0717 13:55:19.673505    1281 control.go:84] [/emulator/tradeshow-os-service 003a] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0717 13:55:19.673505    1281 control.go:84] [/emulator/tradeshow-os-service 003a] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-17T09:55:19.683Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-17T09:55:19.683Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-17T09:55:19.683Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-17T09:55:20.849Z] I0717 13:55:19.815813    1285 load.go:37] Reloading schema and connectors...
I0717 13:55:19.838291    1285 control.go:84] [/emulator/tradeshow-os-service 9b15] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
I0717 13:55:19.852673    1285 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
I0717 13:55:19.852800    1285 load.go:115] Finished reloading
I0717 13:55:19.858048    1285 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0717 13:55:19.815813    1285 load.go:37] Reloading schema and connectors...\nI0717 13:55:19.838291    1285 control.go:84] [/emulator/tradeshow-os-service 9b15] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \nI0717 13:55:19.852673    1285 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \nI0717 13:55:19.852800    1285 load.go:115] Finished reloading\nI0717 13:55:19.858048    1285 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-17T09:55:20.897Z] I0717 13:55:20.896950    1285 engine.go:170] [/emulator/tradeshow-os-service 9b15] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0717 13:55:20.896950    1285 engine.go:170] [/emulator/tradeshow-os-service 9b15] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-18T10:27:21.322Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-18T10:27:29.910Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-18T10:27:29.910Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-18T10:27:29.910Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-18T10:27:29.911Z] Checked if tokens are valid: false, expires at: 1752794767828
[debug] [2025-07-18T10:27:29.911Z] Checked if tokens are valid: false, expires at: 1752794767828
[debug] [2025-07-18T10:27:29.911Z] > refreshing access token with scopes: []
[debug] [2025-07-18T10:27:29.912Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-18T10:27:29.912Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-18T10:27:29.915Z] [Firebase Plugin] VSCode notification server listening on port 40001
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[debug] [2025-07-18T10:27:30.520Z] I0718 14:27:30.509841    4567 control.go:84] [/emulator/tradeshow-os-service f863] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:30.509841    4567 control.go:84] [/emulator/tradeshow-os-service f863] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-18T10:27:30.521Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-18T10:27:30.521Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-18T10:27:30.521Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-18T10:27:30.703Z] I0718 14:27:30.702707    4569 load.go:37] Reloading schema and connectors...
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:30.702707    4569 load.go:37] Reloading schema and connectors...\n"}}
[debug] [2025-07-18T10:27:30.722Z] I0718 14:27:30.722731    4569 control.go:84] [/emulator/tradeshow-os-service 9690] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:30.722731    4569 control.go:84] [/emulator/tradeshow-os-service 9690] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-18T10:27:30.735Z] I0718 14:27:30.735337    4569 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:30.735337    4569 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \n"}}
[debug] [2025-07-18T10:27:30.735Z] I0718 14:27:30.735432    4569 load.go:115] Finished reloading
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:30.735432    4569 load.go:115] Finished reloading\n"}}
[debug] [2025-07-18T10:27:30.741Z] I0718 14:27:30.740994    4569 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:30.740994    4569 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-18T10:27:31.555Z] I0718 14:27:31.555091    4569 engine.go:170] [/emulator/tradeshow-os-service 9690] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0718 14:27:31.555091    4569 engine.go:170] [/emulator/tradeshow-os-service 9690] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[debug] [2025-07-18T10:27:36.234Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-18T10:27:36.234Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-18T10:27:36.246Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-18T10:27:36.247Z] Checked if tokens are valid: true, expires at: 1752838055235
[debug] [2025-07-18T10:27:36.248Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-18T10:27:40.404Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-18T10:27:40.404Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-20T06:16:35.106Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-20T06:16:43.203Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-20T06:16:43.203Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-20T06:16:43.203Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-20T06:16:43.204Z] Checked if tokens are valid: false, expires at: 1752850105775
[debug] [2025-07-20T06:16:43.204Z] Checked if tokens are valid: false, expires at: 1752850105775
[debug] [2025-07-20T06:16:43.204Z] > refreshing access token with scopes: []
[debug] [2025-07-20T06:16:43.205Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-20T06:16:43.205Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-20T06:16:43.208Z] [Firebase Plugin] VSCode notification server listening on port 40001
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[debug] [2025-07-20T06:16:43.696Z] I0720 10:16:43.684024    4068 control.go:84] [/emulator/tradeshow-os-service 31f3] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:43.684024    4068 control.go:84] [/emulator/tradeshow-os-service 31f3] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-20T06:16:43.696Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-20T06:16:43.696Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-20T06:16:43.696Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-20T06:16:43.833Z] I0720 10:16:43.831860    4070 load.go:37] Reloading schema and connectors...
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:43.831860    4070 load.go:37] Reloading schema and connectors...\n"}}
[debug] [2025-07-20T06:16:43.854Z] I0720 10:16:43.854748    4070 control.go:84] [/emulator/tradeshow-os-service fe55] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:43.854748    4070 control.go:84] [/emulator/tradeshow-os-service fe55] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-20T06:16:43.869Z] I0720 10:16:43.868836    4070 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:43.868836    4070 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \n"}}
[debug] [2025-07-20T06:16:43.869Z] I0720 10:16:43.869141    4070 load.go:115] Finished reloading
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:43.869141    4070 load.go:115] Finished reloading\n"}}
[debug] [2025-07-20T06:16:43.876Z] I0720 10:16:43.876328    4070 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:43.876328    4070 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-20T06:16:44.516Z] I0720 10:16:44.516580    4070 engine.go:170] [/emulator/tradeshow-os-service fe55] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0720 10:16:44.516580    4070 engine.go:170] [/emulator/tradeshow-os-service fe55] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[debug] [2025-07-20T06:16:47.366Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-20T06:16:47.366Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-20T06:16:47.400Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-07-20T06:16:47.401Z] Checked if tokens are valid: true, expires at: 1752995806366
[debug] [2025-07-20T06:16:47.402Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-07-20T06:16:51.759Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-20T06:16:51.759Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
[debug] [2025-07-21T08:00:37.576Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Unable to check firebase-tools installation: TypeError: fetch failed
[debug] [2025-07-21T08:00:37.600Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-21T08:00:37.600Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-21T08:00:37.600Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-21T08:00:37.601Z] Checked if tokens are valid: false, expires at: 1752995806366
[debug] [2025-07-21T08:00:37.601Z] Checked if tokens are valid: false, expires at: 1752995806366
[debug] [2025-07-21T08:00:37.601Z] > refreshing access token with scopes: []
[debug] [2025-07-21T08:00:37.602Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-21T08:00:37.602Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-21T08:00:37.605Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-07-21T08:00:37.620Z] *** [apiv2] error from fetch(https://www.googleapis.com/oauth2/v3/token, {"headers":{},"method":"POST","body":{"_overheadLength":545,"_valueLength":212,"_valuesToMeasure":[],"writable":false,"readable":true,"dataSize":0,"maxDataSize":2097152,"pauseStreams":true,"_released":true,"_streams":[],"_currentStream":null,"_insideLoop":false,"_pendingNext":false,"_boundary":"--------------------------668417185416371133529984","_events":{},"_eventsCount":1}}): FetchError: request to https://www.googleapis.com/oauth2/v3/token failed, reason: getaddrinfo ENOTFOUND www.googleapis.com
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] [Firebase Plugin] requireAuth error: Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[info] [Firebase Plugin] Starting Data Connect toolkit (version 2.8.0) on port 50001
[debug] [2025-07-21T08:00:38.008Z] I0721 12:00:37.999431    1334 control.go:84] [/emulator/tradeshow-os-service 5ea7] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0721 12:00:37.999431    1334 control.go:84] [/emulator/tradeshow-os-service 5ea7] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \n"}}
[debug] [2025-07-21T08:00:38.009Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-21T08:00:38.009Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"dataconnect"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-07-21T08:00:38.009Z] Starting Data Connect Emulator with command {"binary":"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0","args":["--logtostderr","-v=2","dev","--listen=localhost:50001","--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect","--enable_output_schema_extensions=true","--enable_output_generated_sdk=true"],"optionalArgs":["listen","config_dir","enable_output_schema_extensions","enable_output_generated_sdk"],"joinArgs":true,"shell":false,"port":9399} {"metadata":{"emulator":{"name":"dataconnect"},"message":"Starting Data Connect Emulator with command {\"binary\":\"/Users/<USER>/.cache/firebase/emulators/dataconnect-emulator-2.8.0\",\"args\":[\"--logtostderr\",\"-v=2\",\"dev\",\"--listen=localhost:50001\",\"--config_dir=/Users/<USER>/Downloads/EVEXA-300625/dataconnect\",\"--enable_output_schema_extensions=true\",\"--enable_output_generated_sdk=true\"],\"optionalArgs\":[\"listen\",\"config_dir\",\"enable_output_schema_extensions\",\"enable_output_generated_sdk\"],\"joinArgs\":true,\"shell\":false,\"port\":9399}"}}
[info] i  dataconnect: Data Connect Emulator logging to dataconnect-debug.log {"metadata":{"emulator":{"name":"dataconnect"},"message":"Data Connect Emulator logging to dataconnect-debug.log"}}
[debug] [2025-07-21T08:00:39.170Z] I0721 12:00:38.151236    1336 load.go:37] Reloading schema and connectors...
I0721 12:00:38.171942    1336 control.go:84] [/emulator/tradeshow-os-service 2ff0] UpdateResources(): done
Schema: sources: schema/schema.gql [2088B] 
Connector "default": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] 
I0721 12:00:38.187249    1336 collector.go:107] schema extensions wrote into "/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema"
Generated sources: prelude.gql [71203B] 
I0721 12:00:38.187362    1336 load.go:115] Finished reloading
I0721 12:00:38.193079    1336 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0721 12:00:38.151236    1336 load.go:37] Reloading schema and connectors...\nI0721 12:00:38.171942    1336 control.go:84] [/emulator/tradeshow-os-service 2ff0] UpdateResources(): done\nSchema: sources: schema/schema.gql [2088B] \nConnector \"default\": sources: connector/mutations.gql [1233B] connector/queries.gql [1802B] \nI0721 12:00:38.187249    1336 collector.go:107] schema extensions wrote into \"/Users/<USER>/Downloads/EVEXA-300625/dataconnect/.dataconnect/schema\"\nGenerated sources: prelude.gql [71203B] \nI0721 12:00:38.187362    1336 load.go:115] Finished reloading\nI0721 12:00:38.193079    1336 dev.go:95] Listening on address (HTTP + gRPC): 127.0.0.1:50001\n"}}
[debug] [2025-07-21T08:00:39.223Z] I0721 12:00:39.222901    1336 engine.go:170] [/emulator/tradeshow-os-service 2ff0] ExecuteGraphqlRead IntrospectionQuery: succeeded. 
 {"metadata":{"emulator":{"name":"dataconnect"},"message":"I0721 12:00:39.222901    1336 engine.go:170] [/emulator/tradeshow-os-service 2ff0] ExecuteGraphqlRead IntrospectionQuery: succeeded. \n"}}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[warn] ⚠  Data Connect Emulator has exited upon receiving signal: SIGINT 
